require('dotenv').config();
const TelegramBot = require('node-telegram-bot-api');
const { createClient } = require('@supabase/supabase-js');
const { Storage } = require('@google-cloud/storage');
const fs = require('fs');
const path = require('path');

/**
 * Enhanced Telegram Bot for FiNManageR
 * 
 * Features:
 * - Enhanced transaction recording with optional attachments
 * - AI-powered financial insights
 * - Bot-based notification system
 * - Enhanced recent transactions with filtering
 * - Additional utility commands
 * - Optimized performance maintained
 */
class EnhancedTelegramBot {
  constructor(token) {
    console.log('🚀 Initializing Enhanced Telegram Bot...');
    
    this.bot = new TelegramBot(token, { polling: true });
    this.supabase = this.initializeSupabase();
    this.storage = this.initializeGoogleStorage();
    
    // Cache for performance optimization
    this.categoriesCache = new Map();
    this.userInsightsCache = new Map();
    this.cacheExpiry = 5 * 60 * 1000; // 5 minutes
    
    // Pending transactions with attachment support
    this.pendingTransactions = new Map();
    this.pendingAttachments = new Map();
    
    // Notification system
    this.notificationQueue = new Map();
    this.notificationScheduler = null;
    
    console.log('🔧 Setting up commands...');
    this.setupCommands();
    
    console.log('🔧 Setting up event handlers...');
    this.setupEventHandlers();
    
    console.log('🔔 Initializing notification system...');
    this.initializeNotificationSystem();
    
    console.log('✅ Enhanced Telegram Bot ready!');
  }

  initializeSupabase() {
    return createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        },
        db: {
          schema: 'public'
        }
      }
    );
  }

  initializeGoogleStorage() {
    try {
      // Initialize Google Cloud Storage for attachments
      const storage = new Storage({
        projectId: process.env.GOOGLE_CLOUD_PROJECT_ID,
        keyFilename: process.env.GOOGLE_CLOUD_KEY_FILE || './google-service-account.json'
      });
      
      this.bucketName = process.env.GOOGLE_CLOUD_BUCKET || 'finmanager-attachments';
      this.bucket = storage.bucket(this.bucketName);
      
      console.log('☁️ Google Cloud Storage initialized');
      return storage;
    } catch (error) {
      console.warn('⚠️ Google Cloud Storage not available:', error.message);
      return null;
    }
  }

  setupCommands() {
    // Enhanced start command
    this.bot.onText(/\/start/, async (msg) => {
      const welcomeMessage = `
🎉 *Welcome to Enhanced FiNManageR Bot!*

Your AI-powered personal finance assistant with advanced features! 💰✨

*🚀 What's New:*
• 📎 **Attachment Support** - Add receipts to your transactions
• 🧠 **AI Financial Insights** - Get personalized spending analysis
• 🔔 **Smart Notifications** - Custom alerts and reminders
• 📊 **Advanced Analytics** - Detailed spending patterns and trends

*📱 Core Commands:*
• \`/help\` - Show all commands
• \`/link <code>\` - Link your FiNManageR account
• \`/categories\` - View your personal categories
• \`/balance\` - Check your current balance
• \`/recent\` - View recent transactions with filters

*🆕 New Commands:*
• \`/budget\` - Set and manage spending budgets
• \`/insights\` - Get AI-powered financial analysis
• \`/export\` - Generate transaction reports
• \`/settings\` - Manage bot preferences
• \`/sync\` - Force sync with web app

*🌟 Smart Features:*
• 💬 **Natural Language**: "Spent 500 on lunch" with optional receipt
• 📸 **Photo Receipts**: Attach bills and receipts to transactions
• 🤖 **AI Insights**: Personalized spending recommendations
• 🔔 **Smart Alerts**: Budget warnings and spending pattern notifications

Ready to supercharge your finances? Let's get started! 🚀

Get your linking code from: https://finmanager.netlify.app → Settings → Telegram Integration
      `;
      
      await this.bot.sendMessage(msg.chat.id, welcomeMessage, { parse_mode: 'Markdown' });
    });

    // Enhanced help command
    this.bot.onText(/\/help/, async (msg) => {
      await this.handleHelpCommand(msg);
    });

    // Advanced help command
    this.bot.onText(/\/help_advanced/, async (msg) => {
      await this.handleAdvancedHelpCommand(msg);
    });

    // Core commands
    this.bot.onText(/\/link (.+)/, async (msg, match) => {
      await this.handleLinkCommand(msg, match[1]);
    });

    this.bot.onText(/\/categories/, async (msg) => {
      await this.handleCategoriesCommand(msg);
    });

    this.bot.onText(/\/balance/, async (msg) => {
      await this.handleBalanceCommand(msg);
    });

    this.bot.onText(/\/recent(?:\s+(.+))?/, async (msg, match) => {
      await this.handleEnhancedRecentCommand(msg, match ? match[1] : null);
    });

    this.bot.onText(/\/expense (.+)/, async (msg, match) => {
      await this.handleExpenseCommand(msg, match[1]);
    });

    this.bot.onText(/\/income (.+)/, async (msg, match) => {
      await this.handleIncomeCommand(msg, match[1]);
    });

    this.bot.onText(/\/status/, async (msg) => {
      await this.handleStatusCommand(msg);
    });

    // New enhanced commands
    this.bot.onText(/\/budget(?:\s+(.+))?/, async (msg, match) => {
      await this.handleBudgetCommand(msg, match ? match[1] : null);
    });

    this.bot.onText(/\/insights/, async (msg) => {
      await this.handleInsightsCommand(msg);
    });

    this.bot.onText(/\/export(?:\s+(.+))?/, async (msg, match) => {
      await this.handleExportCommand(msg, match ? match[1] : null);
    });

    this.bot.onText(/\/settings(?:\s+(.+))?/, async (msg, match) => {
      await this.handleSettingsCommand(msg, match ? match[1] : null);
    });

    this.bot.onText(/\/sync/, async (msg) => {
      await this.handleSyncCommand(msg);
    });
  }

  setupEventHandlers() {
    // Handle non-command messages with enhanced NLP
    this.bot.on('message', async (msg) => {
      if (msg.text && !msg.text.startsWith('/')) {
        await this.handleNaturalLanguageMessage(msg);
      }
    });

    // Handle photo attachments for transactions
    this.bot.on('photo', async (msg) => {
      await this.handlePhotoAttachment(msg);
    });

    // Handle document attachments
    this.bot.on('document', async (msg) => {
      await this.handleDocumentAttachment(msg);
    });

    // Handle voice messages
    this.bot.on('voice', async (msg) => {
      await this.handleVoiceMessage(msg);
    });

    // Handle callback queries for enhanced confirmations
    this.bot.on('callback_query', async (query) => {
      await this.handleCallbackQuery(query);
    });

    // Error handling
    this.bot.on('error', (error) => {
      console.error('Bot error:', error);
    });

    this.bot.on('polling_error', (error) => {
      console.error('Polling error:', error);
    });
  }

  initializeNotificationSystem() {
    // Start notification scheduler (check every 5 minutes)
    this.notificationScheduler = setInterval(async () => {
      await this.processScheduledNotifications();
    }, 5 * 60 * 1000);

    // Process budget alerts (check every hour)
    setInterval(async () => {
      await this.processBudgetAlerts();
    }, 60 * 60 * 1000);

    console.log('🔔 Notification system initialized');
  }

  // Enhanced help command implementation
  async handleHelpCommand(msg) {
    const helpMessage = `
🤖 *Enhanced FiNManageR Bot Commands*

*🔗 Account Management:*
• \`/link <8-digit-code>\` - Link your FiNManageR account
• \`/status\` - Check your account status
• \`/sync\` - Force synchronization with web app

*💰 Transaction Commands:*
• \`/expense <amount> <category> <description>\` - Log expense
• \`/income <amount> <category> <description>\` - Log income
• \`/balance\` - Check your current balance
• \`/recent [filters]\` - View recent transactions
• \`/categories\` - View your personal categories

*📊 Financial Management:*
• \`/budget [category] [amount]\` - Set/view spending budgets
• \`/insights\` - Get AI-powered financial analysis
• \`/export [period]\` - Generate transaction reports

*⚙️ Settings & Preferences:*
• \`/settings [option]\` - Manage bot preferences
• \`/help_advanced\` - Show advanced features

*🌟 Smart Features:*
• 💬 **Natural Language:** "Spent 500 on lunch"
• 📸 **Photo Receipts:** Send photos with transactions
• 🤖 **AI Insights:** Personalized recommendations
• 🔔 **Smart Alerts:** Budget and spending notifications

*💡 Pro Tips:*
• Attach receipts by sending photos before or after transactions
• Use natural language - I understand how you naturally speak
• Set budgets to get automatic spending alerts
• Check insights regularly for financial optimization tips

Need more help? Use \`/help_advanced\` for detailed guides! 🌐
    `;
    
    await this.bot.sendMessage(msg.chat.id, helpMessage, { parse_mode: 'Markdown' });
  }

  async handleAdvancedHelpCommand(msg) {
    const advancedHelpMessage = `
🎓 *Advanced Features Guide*

*📸 Attachment System:*
• Send photos before/after transactions for automatic attachment
• Supported formats: JPG, PNG, PDF documents
• Attachments are optional but recommended for record-keeping
• Files are securely stored in Google Cloud Storage

*🔍 Recent Transactions Filters:*
• \`/recent\` - Last 10 transactions
• \`/recent 20\` - Last 20 transactions
• \`/recent food\` - Filter by category
• \`/recent this month\` - Current month transactions
• \`/recent >1000\` - Transactions above ₹1000

*💰 Budget Management:*
• \`/budget\` - View all budgets
• \`/budget food 5000\` - Set food budget to ₹5000
• \`/budget food\` - View food budget status
• \`/budget reset food\` - Remove food budget

*📊 Export Options:*
• \`/export\` - Current month CSV report
• \`/export last month\` - Previous month report
• \`/export 2024\` - Full year report
• \`/export food\` - Category-specific report

*🔔 Notification Settings:*
• \`/settings notifications on/off\` - Toggle notifications
• \`/settings budget_alerts 80\` - Alert at 80% budget usage
• \`/settings daily_summary on\` - Daily spending summaries
• \`/settings reminders 20:00\` - Set reminder time

*🧠 AI Insights Features:*
• Spending pattern analysis
• Budget optimization suggestions
• Unusual transaction detection
• Monthly financial health reports
• Personalized saving recommendations

*🚀 Power User Tips:*
• Use voice messages for quick expense logging
• Set up recurring transaction templates
• Enable smart notifications for better financial control
• Regular sync ensures data consistency across devices

Questions? Visit: https://finmanager.netlify.app/help 📚
    `;
    
    await this.bot.sendMessage(msg.chat.id, advancedHelpMessage, { parse_mode: 'Markdown' });
  }

  // Photo attachment handler
  async handlePhotoAttachment(msg) {
    try {
      const telegramUserId = msg.from?.id.toString();
      if (!telegramUserId) return;

      const telegramUser = await this.requireAuthentication(msg);
      if (!telegramUser) return;

      // Get the largest photo size
      const photo = msg.photo[msg.photo.length - 1];
      
      await this.bot.sendMessage(msg.chat.id,
        "📸 *Receipt received!*\n\n" +
        "I'll attach this to your next transaction. You can now:\n" +
        "• Say: \"Spent 500 on lunch\" to create transaction with this receipt\n" +
        "• Use: `/expense 500 food lunch` to add structured transaction\n\n" +
        "💡 The attachment will be automatically included in your next transaction.",
        { parse_mode: 'Markdown' }
      );

      // Store pending attachment
      this.pendingAttachments.set(telegramUserId, {
        fileId: photo.file_id,
        fileType: 'photo',
        timestamp: Date.now(),
        caption: msg.caption || null
      });

      // Auto-expire after 10 minutes
      setTimeout(() => {
        if (this.pendingAttachments.has(telegramUserId)) {
          this.pendingAttachments.delete(telegramUserId);
          this.bot.sendMessage(msg.chat.id,
            "⏰ Receipt attachment expired. Please send the photo again if you want to attach it to a transaction."
          );
        }
      }, 10 * 60 * 1000);

    } catch (error) {
      console.error('Photo attachment error:', error);
      await this.bot.sendMessage(msg.chat.id,
        "❌ Failed to process photo attachment. Please try again."
      );
    }
  }

  // Document attachment handler
  async handleDocumentAttachment(msg) {
    try {
      const telegramUserId = msg.from?.id.toString();
      if (!telegramUserId) return;

      const telegramUser = await this.requireAuthentication(msg);
      if (!telegramUser) return;

      const document = msg.document;
      
      // Check file type
      const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png'];
      if (!allowedTypes.includes(document.mime_type)) {
        await this.bot.sendMessage(msg.chat.id,
          "❌ Unsupported file type. Please send PDF, JPG, or PNG files only."
        );
        return;
      }

      await this.bot.sendMessage(msg.chat.id,
        "📄 *Document received!*\n\n" +
        `File: ${document.file_name}\n` +
        "I'll attach this to your next transaction.\n\n" +
        "💡 Create a transaction now to include this attachment.",
        { parse_mode: 'Markdown' }
      );

      // Store pending attachment
      this.pendingAttachments.set(telegramUserId, {
        fileId: document.file_id,
        fileType: 'document',
        fileName: document.file_name,
        mimeType: document.mime_type,
        timestamp: Date.now(),
        caption: msg.caption || null
      });

      // Auto-expire after 10 minutes
      setTimeout(() => {
        if (this.pendingAttachments.has(telegramUserId)) {
          this.pendingAttachments.delete(telegramUserId);
          this.bot.sendMessage(msg.chat.id,
            "⏰ Document attachment expired. Please send the file again if you want to attach it to a transaction."
          );
        }
      }, 10 * 60 * 1000);

    } catch (error) {
      console.error('Document attachment error:', error);
      await this.bot.sendMessage(msg.chat.id,
        "❌ Failed to process document attachment. Please try again."
      );
    }
  }

  // Voice message handler
  async handleVoiceMessage(msg) {
    try {
      const telegramUserId = msg.from?.id.toString();
      if (!telegramUserId) return;

      const telegramUser = await this.requireAuthentication(msg);
      if (!telegramUser) return;

      await this.bot.sendMessage(msg.chat.id,
        "🎤 *Voice message received!*\n\n" +
        "Voice-to-text processing is coming soon! For now, please:\n" +
        "• Type your transaction: \"Spent 500 on lunch\"\n" +
        "• Or use commands: `/expense 500 food lunch`\n\n" +
        "💡 Voice processing will be available in the next update.",
        { parse_mode: 'Markdown' }
      );

    } catch (error) {
      console.error('Voice message error:', error);
      await this.bot.sendMessage(msg.chat.id,
        "❌ Failed to process voice message. Please try typing your transaction."
      );
    }
  }

  // Enhanced natural language processing with attachment support
  async handleNaturalLanguageMessage(msg) {
    try {
      const telegramUserId = msg.from?.id.toString();
      if (!telegramUserId) return;

      // Check authentication
      const telegramUser = await this.requireAuthentication(msg);
      if (!telegramUser) return;

      // Parse natural language
      const nlpResult = this.parseNaturalLanguage(msg.text);

      if (nlpResult.success && nlpResult.transaction) {
        const transaction = nlpResult.transaction;

        // Check for pending attachment
        const pendingAttachment = this.pendingAttachments.get(telegramUserId);
        const hasAttachment = !!pendingAttachment;

        // Show enhanced confirmation with attachment info
        const confirmationMessage = `
🤖 *I understood your transaction:*

*Amount:* ₹${transaction.amount.toLocaleString()}
*Type:* ${transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)}
*Category:* ${transaction.category}
*Description:* ${transaction.description}
*Confidence:* ${nlpResult.confidence}%
*Attachment:* ${hasAttachment ? '✅ Yes' : '❌ No'}

${hasAttachment ? `📎 *Attached:* ${pendingAttachment.fileType === 'photo' ? 'Receipt Photo' : pendingAttachment.fileName || 'Document'}\n` : ''}
Is this correct?
        `;

        const keyboard = {
          inline_keyboard: [
            [
              { text: '✅ Confirm', callback_data: `confirm_${Date.now()}` },
              { text: '❌ Cancel', callback_data: `cancel_${Date.now()}` }
            ]
          ]
        };

        // Store pending transaction with attachment info
        const transactionId = Date.now().toString();
        this.pendingTransactions.set(transactionId, {
          userId: telegramUser.user_id,
          telegramUserId: telegramUserId,
          transaction: transaction,
          attachment: pendingAttachment || null,
          originalMessage: msg.text,
          timestamp: Date.now()
        });

        // Update keyboard with correct transaction ID
        keyboard.inline_keyboard[0][0].callback_data = `confirm_${transactionId}`;
        keyboard.inline_keyboard[0][1].callback_data = `cancel_${transactionId}`;

        await this.bot.sendMessage(msg.chat.id, confirmationMessage, {
          parse_mode: 'Markdown',
          reply_markup: keyboard
        });

      } else {
        // Provide helpful suggestions
        const suggestions = this.getNLPSuggestions(msg.text);
        let message = "🤔 I couldn't understand that transaction.\n\n" +
          "Try these formats:\n" +
          "• \"Spent 500 on lunch\"\n" +
          "• \"Paid 1200 for groceries\"\n" +
          "• \"Earned 50000 from salary\"\n\n";

        if (suggestions.length > 0) {
          message += "*💡 Suggestions:*\n" + suggestions.join('\n') + "\n\n";
        }

        message += "Or use commands like `/expense 500 food lunch` for direct entry.";

        await this.bot.sendMessage(msg.chat.id, message, { parse_mode: 'Markdown' });
      }

    } catch (error) {
      console.error('Natural language processing error:', error);
      await this.bot.sendMessage(msg.chat.id,
        "❌ I'm having trouble processing your message right now. Please try again or use specific commands.\n\n" +
        "💡 Try: `/expense 500 food Lunch` or `/income 50000 salary`"
      );
    }
  }

  // Enhanced callback query handler with attachment support
  async handleCallbackQuery(query) {
    try {
      const [action, transactionId] = query.data.split('_');
      const pendingTransaction = this.pendingTransactions.get(transactionId);

      if (!pendingTransaction) {
        await this.bot.answerCallbackQuery(query.id, {
          text: "Transaction expired. Please try again.",
          show_alert: true
        });
        return;
      }

      if (action === 'confirm') {
        // Upload attachment if present
        let attachmentUrl = null;
        if (pendingTransaction.attachment) {
          attachmentUrl = await this.uploadAttachment(
            pendingTransaction.attachment,
            pendingTransaction.telegramUserId
          );
        }

        // Create the transaction with attachment
        const { error } = await this.supabase
          .from('transactions')
          .insert({
            user_id: pendingTransaction.userId,
            amount: pendingTransaction.transaction.amount,
            type: pendingTransaction.transaction.type,
            category: pendingTransaction.transaction.category,
            description: pendingTransaction.transaction.description,
            date: new Date().toISOString(),
            attachment_url: attachmentUrl,
            metadata: {
              telegram_user_id: pendingTransaction.telegramUserId,
              source_type: 'telegram_bot_nlp',
              original_message: pendingTransaction.originalMessage,
              has_attachment: !!attachmentUrl,
              attachment_type: pendingTransaction.attachment?.fileType || null
            }
          });

        if (error) throw error;

        const emoji = pendingTransaction.transaction.type === 'income' ? '💰' : '💸';
        const attachmentInfo = attachmentUrl ? '\n📎 *Attachment:* Saved successfully' : '\n📎 *Attachment:* None';

        await this.bot.editMessageText(
          `${emoji} *Transaction Confirmed!*\n\n` +
          `₹${pendingTransaction.transaction.amount.toLocaleString()} ${pendingTransaction.transaction.type} recorded successfully.${attachmentInfo}\n\n` +
          `Use \`/balance\` to see your updated balance.`,
          {
            chat_id: query.message.chat.id,
            message_id: query.message.message_id,
            parse_mode: 'Markdown'
          }
        );

        await this.bot.answerCallbackQuery(query.id, {
          text: "Transaction recorded successfully!",
          show_alert: false
        });

        // Clean up pending attachment
        if (pendingTransaction.attachment) {
          this.pendingAttachments.delete(pendingTransaction.telegramUserId);
        }

      } else if (action === 'cancel') {
        await this.bot.editMessageText(
          "❌ Transaction cancelled.\n\nTry again with a different format or use commands like `/expense 500 food lunch`.",
          {
            chat_id: query.message.chat.id,
            message_id: query.message.message_id
          }
        );

        await this.bot.answerCallbackQuery(query.id, {
          text: "Transaction cancelled",
          show_alert: false
        });
      }

      // Clean up pending transaction
      this.pendingTransactions.delete(transactionId);

    } catch (error) {
      console.error('Callback query error:', error);
      await this.bot.answerCallbackQuery(query.id, {
        text: "Error processing transaction. Please try again.",
        show_alert: true
      });
    }
  }

  // Upload attachment to Google Cloud Storage
  async uploadAttachment(attachment, telegramUserId) {
    try {
      if (!this.storage || !this.bucket) {
        console.warn('Google Cloud Storage not available');
        return null;
      }

      // Get file from Telegram
      const fileLink = await this.bot.getFileLink(attachment.fileId);
      const response = await fetch(fileLink);
      const buffer = await response.buffer();

      // Generate unique filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const extension = attachment.fileType === 'photo' ? 'jpg' :
                      attachment.fileName ? path.extname(attachment.fileName) : '.pdf';
      const fileName = `telegram-attachments/${telegramUserId}/${timestamp}${extension}`;

      // Upload to Google Cloud Storage
      const file = this.bucket.file(fileName);
      await file.save(buffer, {
        metadata: {
          contentType: attachment.mimeType || 'image/jpeg',
          metadata: {
            telegramUserId: telegramUserId,
            originalFileName: attachment.fileName || 'photo',
            uploadedAt: new Date().toISOString(),
            source: 'telegram_bot'
          }
        }
      });

      // Make file publicly accessible
      await file.makePublic();

      // Return public URL
      return `https://storage.googleapis.com/${this.bucketName}/${fileName}`;

    } catch (error) {
      console.error('Attachment upload error:', error);
      return null;
    }
  }

  // Link command handler
  async handleLinkCommand(msg, code) {
    try {
      const telegramUserId = msg.from?.id.toString();
      if (!telegramUserId) return;

      if (!code || code.length !== 8) {
        await this.bot.sendMessage(msg.chat.id,
          "❌ Invalid code format. Please provide an 8-digit code.\n\n" +
          "Get your code from: https://finmanager.netlify.app → Settings → Telegram Integration"
        );
        return;
      }

      // Check if code exists and is valid
      const { data: authCode, error: codeError } = await this.supabase
        .from('telegram_auth_codes')
        .select('*')
        .eq('code', code)
        .eq('is_used', false)
        .single();

      if (codeError || !authCode) {
        await this.bot.sendMessage(msg.chat.id,
          "❌ Invalid or expired code. Please generate a new code from the web app.\n\n" +
          "Get your code from: https://finmanager.netlify.app → Settings → Telegram Integration"
        );
        return;
      }

      // Check if user is already linked
      const { data: existingUser } = await this.supabase
        .from('telegram_users')
        .select('*')
        .eq('telegram_id', telegramUserId)
        .eq('is_active', true)
        .single();

      if (existingUser) {
        await this.bot.sendMessage(msg.chat.id,
          "✅ Your account is already linked!\n\n" +
          "🚀 *Enhanced Features Available:*\n" +
          "• 📸 Attach receipts to transactions\n" +
          "• 🧠 Get AI financial insights\n" +
          "• 🔔 Receive smart notifications\n" +
          "• 📊 Advanced transaction analytics\n\n" +
          "Try: \"Spent 500 on lunch\" or `/insights` to get started!",
          { parse_mode: 'Markdown' }
        );
        return;
      }

      // Create telegram user record
      const { error: linkError } = await this.supabase
        .from('telegram_users')
        .insert({
          telegram_id: telegramUserId,
          user_id: authCode.user_id,
          username: msg.from.username || null,
          first_name: msg.from.first_name || null,
          last_name: msg.from.last_name || null,
          is_active: true,
          linked_at: new Date().toISOString(),
          preferences: {
            notifications_enabled: true,
            budget_alerts: true,
            daily_summary: false,
            spending_insights: true
          }
        });

      if (linkError) throw linkError;

      // Mark code as used
      await this.supabase
        .from('telegram_auth_codes')
        .update({ is_used: true, used_at: new Date().toISOString() })
        .eq('code', code);

      await this.bot.sendMessage(msg.chat.id,
        "🎉 *Account Successfully Linked!*\n\n" +
        "Your Telegram account is now connected to FiNManageR with enhanced features!\n\n" +
        "*🚀 You can now:*\n" +
        "• 📸 **Attach Receipts**: Send photos with transactions\n" +
        "• 🧠 **AI Insights**: Get personalized financial analysis\n" +
        "• 🔔 **Smart Alerts**: Receive budget and spending notifications\n" +
        "• 📊 **Advanced Analytics**: Detailed spending patterns\n\n" +
        "*💡 Try these enhanced features:*\n" +
        "• \"Spent 500 on food\" (with photo attachment)\n" +
        "• `/insights` - Get AI financial analysis\n" +
        "• `/budget food 5000` - Set spending budgets\n" +
        "• `/settings` - Configure preferences\n\n" +
        "*🎯 Quick Start:* Send a photo of a receipt, then say \"Spent 500 on lunch\"!",
        { parse_mode: 'Markdown' }
      );

    } catch (error) {
      console.error('Link command error:', error);
      await this.bot.sendMessage(msg.chat.id,
        "❌ Failed to link account. Please try again.\n\n" +
        "If the problem persists, generate a new code from the web app."
      );
    }
  }

  // Categories command handler (enhanced)
  async handleCategoriesCommand(msg) {
    try {
      const telegramUserId = msg.from?.id.toString();
      if (!telegramUserId) return;

      // Check authentication
      const telegramUser = await this.requireAuthentication(msg);
      if (!telegramUser) return;

      // Check cache first
      const cacheKey = `categories_${telegramUser.user_id}`;
      const cached = this.categoriesCache.get(cacheKey);

      if (cached && (Date.now() - cached.timestamp) < this.cacheExpiry) {
        await this.bot.sendMessage(msg.chat.id, cached.message, { parse_mode: 'Markdown' });
        return;
      }

      // Fetch user's categories from database
      const { data: categories, error } = await this.supabase
        .from('categories')
        .select('*')
        .eq('user_id', telegramUser.user_id)
        .order('name');

      if (error) throw error;

      // Separate categories by type
      const expenseCategories = categories.filter(cat => cat.type === 'expense');
      const incomeCategories = categories.filter(cat => cat.type === 'income');

      // Format categories with enhanced info
      const expenseCategoriesText = expenseCategories.length > 0
        ? expenseCategories.map(cat => {
            const icon = cat.icon || '💰';
            const isDefault = cat.is_default ? '' : ' (Custom)';
            return `${icon} \`${cat.name.toLowerCase().replace(/[^a-z0-9]/g, '')}\` - ${cat.name}${isDefault}`;
          }).join('\n')
        : '• No expense categories found';

      const incomeCategoriesText = incomeCategories.length > 0
        ? incomeCategories.map(cat => {
            const icon = cat.icon || '💵';
            const isDefault = cat.is_default ? '' : ' (Custom)';
            return `${icon} \`${cat.name.toLowerCase().replace(/[^a-z0-9]/g, '')}\` - ${cat.name}${isDefault}`;
          }).join('\n')
        : '• No income categories found';

      const categoriesMessage = `
📂 *Your Personal Categories*

*💸 Expense Categories (${expenseCategories.length}):*
${expenseCategoriesText}

*💰 Income Categories (${incomeCategories.length}):*
${incomeCategoriesText}

*📝 Usage Examples:*
• \`/expense 500 ${expenseCategories[0]?.name.toLowerCase().replace(/[^a-z0-9]/g, '') || 'food'} Lunch at restaurant\`
• \`/income 50000 ${incomeCategories[0]?.name.toLowerCase().replace(/[^a-z0-9]/g, '') || 'salary'} Monthly salary\`

*🎯 Natural Language Examples:*
• "Spent 500 on ${expenseCategories[0]?.name || 'food'}" (with photo attachment)
• "Received 50000 from ${incomeCategories[0]?.name || 'salary'}"

*✨ Enhanced Features:*
• 📸 Attach receipts by sending photos before transactions
• 🔔 Set budgets: \`/budget ${expenseCategories[0]?.name.toLowerCase().replace(/[^a-z0-9]/g, '') || 'food'} 5000\`
• 📊 Get insights: \`/insights\` for spending analysis

💾 *Categories synced with your FiNManageR web app!*
      `;

      // Cache the result
      this.categoriesCache.set(cacheKey, {
        message: categoriesMessage,
        timestamp: Date.now()
      });

      await this.bot.sendMessage(msg.chat.id, categoriesMessage, { parse_mode: 'Markdown' });

    } catch (error) {
      console.error('Categories command error:', error);
      await this.bot.sendMessage(msg.chat.id,
        "❌ Failed to retrieve categories. Please try again.\n\n" +
        "If the problem persists, contact support through the web app."
      );
    }
  }

  // Enhanced balance command with insights
  async handleBalanceCommand(msg) {
    try {
      const telegramUserId = msg.from?.id.toString();
      if (!telegramUserId) return;

      const telegramUser = await this.requireAuthentication(msg);
      if (!telegramUser) return;

      // Get user's transactions to calculate balance
      const { data: transactions, error } = await this.supabase
        .from('transactions')
        .select('amount, type, category, date, attachment_url')
        .eq('user_id', telegramUser.user_id);

      if (error) throw error;

      let totalIncome = 0;
      let totalExpenses = 0;
      let transactionsWithAttachments = 0;
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();
      let monthlyIncome = 0;
      let monthlyExpenses = 0;

      transactions.forEach(transaction => {
        const transactionDate = new Date(transaction.date);
        const isCurrentMonth = transactionDate.getMonth() === currentMonth &&
                              transactionDate.getFullYear() === currentYear;

        if (transaction.type === 'income') {
          totalIncome += transaction.amount;
          if (isCurrentMonth) monthlyIncome += transaction.amount;
        } else if (transaction.type === 'expense') {
          totalExpenses += transaction.amount;
          if (isCurrentMonth) monthlyExpenses += transaction.amount;
        }

        if (transaction.attachment_url) {
          transactionsWithAttachments++;
        }
      });

      const balance = totalIncome - totalExpenses;
      const monthlyBalance = monthlyIncome - monthlyExpenses;
      const balanceEmoji = balance >= 0 ? '💰' : '⚠️';
      const monthlyEmoji = monthlyBalance >= 0 ? '📈' : '📉';
      const attachmentRate = transactions.length > 0 ?
        Math.round((transactionsWithAttachments / transactions.length) * 100) : 0;

      const balanceMessage = `
${balanceEmoji} *Your Financial Summary*

*💰 Overall Balance:*
• Total Income: ₹${totalIncome.toLocaleString()}
• Total Expenses: ₹${totalExpenses.toLocaleString()}
• **Net Balance: ₹${balance.toLocaleString()}**

*📊 This Month (${new Date().toLocaleString('default', { month: 'long' })}):*
${monthlyEmoji} Monthly Income: ₹${monthlyIncome.toLocaleString()}
${monthlyEmoji} Monthly Expenses: ₹${monthlyExpenses.toLocaleString()}
${monthlyEmoji} **Monthly Balance: ₹${monthlyBalance.toLocaleString()}**

*📈 Quick Stats:*
• Total Transactions: ${transactions.length}
• With Attachments: ${transactionsWithAttachments} (${attachmentRate}%)
• Average Transaction: ₹${transactions.length > 0 ? Math.round((totalIncome + totalExpenses) / transactions.length).toLocaleString() : '0'}

${balance < 0 ? '⚠️ *Note:* Your expenses exceed your income. Consider reviewing your spending patterns.' : '✅ *Great!* You\'re maintaining a positive balance.'}

*🚀 Quick Actions:*
• \`/recent\` - View recent transactions
• \`/insights\` - Get AI financial analysis
• \`/budget\` - Set spending budgets
• \`/export\` - Generate detailed reports
      `;

      await this.bot.sendMessage(msg.chat.id, balanceMessage, { parse_mode: 'Markdown' });

    } catch (error) {
      console.error('Balance command error:', error);
      await this.bot.sendMessage(msg.chat.id,
        "❌ Failed to retrieve balance. Please try again.\n\n" +
        "If the problem persists, contact support through the web app."
      );
    }
  }

  // Enhanced recent transactions with filtering
  async handleEnhancedRecentCommand(msg, filters) {
    try {
      const telegramUserId = msg.from?.id.toString();
      if (!telegramUserId) return;

      const telegramUser = await this.requireAuthentication(msg);
      if (!telegramUser) return;

      // Parse filters
      let limit = 10;
      let categoryFilter = null;
      let amountFilter = null;
      let dateFilter = null;

      if (filters) {
        const filterParts = filters.toLowerCase().trim();

        // Check for number (limit)
        const numberMatch = filterParts.match(/^\d+$/);
        if (numberMatch) {
          limit = Math.min(parseInt(numberMatch[0]), 50); // Max 50 transactions
        }

        // Check for category filter
        if (filterParts.includes('food') || filterParts.includes('transport') ||
            filterParts.includes('entertainment') || filterParts.includes('salary')) {
          categoryFilter = filterParts;
        }

        // Check for amount filter (>1000, <500, etc.)
        const amountMatch = filterParts.match(/([><]=?)(\d+)/);
        if (amountMatch) {
          amountFilter = {
            operator: amountMatch[1],
            value: parseInt(amountMatch[2])
          };
        }

        // Check for date filter
        if (filterParts.includes('this month') || filterParts.includes('today') ||
            filterParts.includes('this week')) {
          dateFilter = filterParts;
        }
      }

      // Build query
      let query = this.supabase
        .from('transactions')
        .select('*')
        .eq('user_id', telegramUser.user_id)
        .order('created_at', { ascending: false });

      // Apply filters
      if (categoryFilter) {
        query = query.ilike('category', `%${categoryFilter}%`);
      }

      if (dateFilter) {
        const now = new Date();
        let startDate;

        if (dateFilter.includes('today')) {
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        } else if (dateFilter.includes('this week')) {
          const dayOfWeek = now.getDay();
          startDate = new Date(now.getTime() - dayOfWeek * 24 * 60 * 60 * 1000);
        } else if (dateFilter.includes('this month')) {
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        }

        if (startDate) {
          query = query.gte('date', startDate.toISOString());
        }
      }

      query = query.limit(limit);

      const { data: transactions, error } = await query;

      if (error) throw error;

      // Apply amount filter (post-query since Supabase doesn't support complex operators easily)
      let filteredTransactions = transactions;
      if (amountFilter) {
        filteredTransactions = transactions.filter(t => {
          switch (amountFilter.operator) {
            case '>': return t.amount > amountFilter.value;
            case '<': return t.amount < amountFilter.value;
            case '>=': return t.amount >= amountFilter.value;
            case '<=': return t.amount <= amountFilter.value;
            default: return true;
          }
        });
      }

      if (!filteredTransactions || filteredTransactions.length === 0) {
        const filterInfo = filters ? ` matching "${filters}"` : '';
        await this.bot.sendMessage(msg.chat.id,
          `📝 *No transactions found${filterInfo}*\n\n` +
          "Start tracking your expenses:\n" +
          "• Say: \"Spent 500 on lunch\" (with photo attachment)\n" +
          "• Or use: `/expense 500 food lunch`\n\n" +
          "*🔍 Filter Examples:*\n" +
          "• `/recent 20` - Last 20 transactions\n" +
          "• `/recent food` - Food category only\n" +
          "• `/recent >1000` - Transactions above ₹1000\n" +
          "• `/recent this month` - Current month only",
          { parse_mode: 'Markdown' }
        );
        return;
      }

      const filterInfo = filters ? ` (filtered: ${filters})` : '';
      let recentMessage = `📋 *Your Recent Transactions${filterInfo}*\n\n`;

      filteredTransactions.forEach((transaction, index) => {
        const emoji = transaction.type === 'income' ? '💰' : '💸';
        const date = new Date(transaction.created_at).toLocaleDateString();
        const amount = transaction.amount.toLocaleString();
        const attachmentIcon = transaction.attachment_url ? '📎' : '';

        recentMessage += `${emoji} **₹${amount}** - ${transaction.category} ${attachmentIcon}\n`;
        recentMessage += `   ${transaction.description || 'No description'}\n`;
        recentMessage += `   📅 ${date}\n\n`;
      });

      recentMessage += `*📊 Summary:* ${filteredTransactions.length} transactions shown\n`;
      recentMessage += `*📎 Attachments:* ${filteredTransactions.filter(t => t.attachment_url).length} with receipts\n\n`;
      recentMessage += `*🔍 More Filters:*\n`;
      recentMessage += `• \`/recent 20\` - Show more transactions\n`;
      recentMessage += `• \`/recent food\` - Filter by category\n`;
      recentMessage += `• \`/recent >1000\` - Amount filters\n`;
      recentMessage += `• \`/recent this month\` - Date filters\n\n`;
      recentMessage += `💡 *Tip:* Use \`/insights\` for AI analysis of your spending patterns.`;

      await this.bot.sendMessage(msg.chat.id, recentMessage, { parse_mode: 'Markdown' });

    } catch (error) {
      console.error('Recent command error:', error);
      await this.bot.sendMessage(msg.chat.id,
        "❌ Failed to retrieve recent transactions. Please try again.\n\n" +
        "If the problem persists, contact support through the web app."
      );
    }
  }

  // Enhanced expense command with attachment support
  async handleExpenseCommand(msg, params) {
    await this.handleTransactionCommand(msg, params, 'expense');
  }

  // Enhanced income command with attachment support
  async handleIncomeCommand(msg, params) {
    await this.handleTransactionCommand(msg, params, 'income');
  }

  // Enhanced transaction command handler with attachment support
  async handleTransactionCommand(msg, params, type) {
    try {
      const telegramUserId = msg.from?.id.toString();
      if (!telegramUserId) return;

      const telegramUser = await this.requireAuthentication(msg);
      if (!telegramUser) return;

      // Parse parameters: amount category description
      const parts = params.trim().split(' ');
      if (parts.length < 2) {
        await this.bot.sendMessage(msg.chat.id,
          `❌ Invalid format. Use: \`/${type} <amount> <category> <description>\`\n\n` +
          `Example: \`/${type} 500 food Lunch at restaurant\`\n\n` +
          `💡 Tips:\n` +
          `• Use \`/categories\` to see your personal ${type} categories\n` +
          `• Send a photo before the command to attach a receipt\n` +
          `• Or try natural language: "Spent 500 on lunch"`,
          { parse_mode: 'Markdown' }
        );
        return;
      }

      const amount = parseFloat(parts[0]);
      const category = parts[1];
      const description = parts.slice(2).join(' ') || `${type} transaction`;

      if (isNaN(amount) || amount <= 0) {
        await this.bot.sendMessage(msg.chat.id,
          "❌ Invalid amount. Please enter a valid positive number.\n\n" +
          `Example: \`/${type} 500 food Lunch\``,
          { parse_mode: 'Markdown' }
        );
        return;
      }

      // Check for pending attachment
      const pendingAttachment = this.pendingAttachments.get(telegramUserId);
      const hasAttachment = !!pendingAttachment;

      // Upload attachment if present
      let attachmentUrl = null;
      if (pendingAttachment) {
        attachmentUrl = await this.uploadAttachment(pendingAttachment, telegramUserId);
      }

      // Create transaction
      const { data: transaction, error } = await this.supabase
        .from('transactions')
        .insert({
          user_id: telegramUser.user_id,
          amount: amount,
          type: type,
          category: category,
          description: description,
          date: new Date().toISOString(),
          attachment_url: attachmentUrl,
          metadata: {
            telegram_user_id: telegramUserId,
            telegram_username: msg.from.username || null,
            source_type: 'telegram_bot_command',
            has_attachment: !!attachmentUrl,
            attachment_type: pendingAttachment?.fileType || null
          }
        })
        .select()
        .single();

      if (error) throw error;

      const emoji = type === 'income' ? '💰' : '💸';
      const attachmentInfo = attachmentUrl ? '\n📎 **Attachment:** Receipt saved successfully' :
                            hasAttachment ? '\n📎 **Attachment:** Failed to save (will retry)' :
                            '\n📎 **Attachment:** None';

      const successMessage = `
${emoji} *Transaction Recorded Successfully!*

*Amount:* ₹${amount.toLocaleString()}
*Type:* ${type.charAt(0).toUpperCase() + type.slice(1)}
*Category:* ${category}
*Description:* ${description}
*Date:* ${new Date().toLocaleDateString()}${attachmentInfo}

✅ Your transaction has been saved to your FiNManageR account.

*🚀 Quick Actions:*
• \`/balance\` - See your updated balance
• \`/recent\` - View recent transactions
• \`/insights\` - Get AI spending analysis
• \`/budget ${category}\` - Set budget for this category
      `;

      await this.bot.sendMessage(msg.chat.id, successMessage, { parse_mode: 'Markdown' });

      // Clean up pending attachment
      if (pendingAttachment) {
        this.pendingAttachments.delete(telegramUserId);
      }

      // Trigger budget alert check if it's an expense
      if (type === 'expense') {
        await this.checkBudgetAlert(telegramUser.user_id, category, amount, telegramUserId);
      }

    } catch (error) {
      console.error('Transaction command error:', error);
      await this.bot.sendMessage(msg.chat.id,
        `❌ Failed to record ${type}. Please try again.\n\n` +
        "If the problem persists, contact support through the web app."
      );
    }
  }

  // Status command handler
  async handleStatusCommand(msg) {
    try {
      const telegramUserId = msg.from?.id.toString();
      if (!telegramUserId) return;

      const { data: telegramUser } = await this.supabase
        .from('telegram_users')
        .select('*')
        .eq('telegram_id', telegramUserId)
        .eq('is_active', true)
        .single();

      if (!telegramUser) {
        await this.bot.sendMessage(msg.chat.id,
          "🔗 *Account Not Linked*\n\n" +
          "Your Telegram account is not linked to FiNManageR.\n\n" +
          "To get started:\n" +
          "1. Visit: https://finmanager.netlify.app\n" +
          "2. Go to Settings → Telegram Integration\n" +
          "3. Generate an 8-digit code\n" +
          "4. Use: `/link <your-code>`\n\n" +
          "🚀 Once linked, you'll get access to enhanced features like receipt attachments and AI insights!",
          { parse_mode: 'Markdown' }
        );
        return;
      }

      // Get user's transaction stats
      const { data: transactions } = await this.supabase
        .from('transactions')
        .select('id, attachment_url, created_at')
        .eq('user_id', telegramUser.user_id);

      const totalTransactions = transactions?.length || 0;
      const transactionsWithAttachments = transactions?.filter(t => t.attachment_url).length || 0;
      const attachmentRate = totalTransactions > 0 ?
        Math.round((transactionsWithAttachments / totalTransactions) * 100) : 0;

      const lastTransaction = transactions?.length > 0 ?
        new Date(transactions[0].created_at).toLocaleDateString() : 'Never';

      const preferences = telegramUser.preferences || {};
      const notificationsStatus = preferences.notifications_enabled ? '✅ Enabled' : '❌ Disabled';
      const budgetAlertsStatus = preferences.budget_alerts ? '✅ Enabled' : '❌ Disabled';

      const statusMessage = `
✅ *Account Status: Linked & Enhanced*

*👤 Account Details:*
• Name: ${telegramUser.first_name || 'Not set'} ${telegramUser.last_name || ''}
• Username: @${telegramUser.username || 'Not set'}
• Linked: ${new Date(telegramUser.linked_at).toLocaleDateString()}

*📊 Usage Statistics:*
• Total Transactions: ${totalTransactions}
• With Attachments: ${transactionsWithAttachments} (${attachmentRate}%)
• Last Transaction: ${lastTransaction}

*🔔 Notification Settings:*
• Notifications: ${notificationsStatus}
• Budget Alerts: ${budgetAlertsStatus}
• Daily Summary: ${preferences.daily_summary ? '✅ Enabled' : '❌ Disabled'}

*🚀 Enhanced Features Available:*
• ✅ **Receipt Attachments** - Photo & document support
• ✅ **AI Financial Insights** - Personalized analysis
• ✅ **Smart Notifications** - Budget & spending alerts
• ✅ **Advanced Analytics** - Detailed reporting
• ✅ **Natural Language Processing** - Conversational interface

*💡 Quick Actions:*
• \`/settings\` - Configure preferences
• \`/insights\` - Get AI financial analysis
• \`/budget\` - Manage spending budgets
• \`/sync\` - Force data synchronization

🎯 **Ready to use!** Try: "Spent 500 on lunch" with a photo attachment!
      `;

      await this.bot.sendMessage(msg.chat.id, statusMessage, { parse_mode: 'Markdown' });

    } catch (error) {
      console.error('Status command error:', error);
      await this.bot.sendMessage(msg.chat.id,
        "❌ Failed to check status. Please try again."
      );
    }
  }

  // Budget management command
  async handleBudgetCommand(msg, params) {
    try {
      const telegramUserId = msg.from?.id.toString();
      if (!telegramUserId) return;

      const telegramUser = await this.requireAuthentication(msg);
      if (!telegramUser) return;

      if (!params) {
        // Show all budgets
        await this.showAllBudgets(msg, telegramUser);
        return;
      }

      const parts = params.trim().split(' ');
      const category = parts[0].toLowerCase();
      const amount = parts[1];

      if (parts.length === 1) {
        // Show specific category budget
        await this.showCategoryBudget(msg, telegramUser, category);
      } else if (parts.length === 2) {
        if (amount === 'reset' || amount === 'remove') {
          // Remove budget
          await this.removeBudget(msg, telegramUser, category);
        } else {
          // Set budget
          const budgetAmount = parseFloat(amount);
          if (isNaN(budgetAmount) || budgetAmount <= 0) {
            await this.bot.sendMessage(msg.chat.id,
              "❌ Invalid budget amount. Please enter a positive number.\n\n" +
              "Example: `/budget food 5000`"
            );
            return;
          }
          await this.setBudget(msg, telegramUser, category, budgetAmount);
        }
      } else {
        await this.bot.sendMessage(msg.chat.id,
          "❌ Invalid format. Use:\n\n" +
          "• `/budget` - View all budgets\n" +
          "• `/budget food` - View food budget\n" +
          "• `/budget food 5000` - Set food budget to ₹5000\n" +
          "• `/budget food reset` - Remove food budget"
        );
      }

    } catch (error) {
      console.error('Budget command error:', error);
      await this.bot.sendMessage(msg.chat.id,
        "❌ Failed to process budget command. Please try again."
      );
    }
  }

  // AI-powered insights command
  async handleInsightsCommand(msg) {
    try {
      const telegramUserId = msg.from?.id.toString();
      if (!telegramUserId) return;

      const telegramUser = await this.requireAuthentication(msg);
      if (!telegramUser) return;

      await this.bot.sendMessage(msg.chat.id,
        "🧠 *Generating AI Financial Insights...*\n\n" +
        "Analyzing your spending patterns, trends, and providing personalized recommendations...",
        { parse_mode: 'Markdown' }
      );

      // Check cache first
      const cacheKey = `insights_${telegramUser.user_id}`;
      const cached = this.userInsightsCache.get(cacheKey);

      if (cached && (Date.now() - cached.timestamp) < (30 * 60 * 1000)) { // 30 minutes cache
        await this.bot.sendMessage(msg.chat.id, cached.insights, { parse_mode: 'Markdown' });
        return;
      }

      // Generate fresh insights
      const insights = await this.generateAIInsights(telegramUser.user_id);

      // Cache the insights
      this.userInsightsCache.set(cacheKey, {
        insights: insights,
        timestamp: Date.now()
      });

      await this.bot.sendMessage(msg.chat.id, insights, { parse_mode: 'Markdown' });

    } catch (error) {
      console.error('Insights command error:', error);
      await this.bot.sendMessage(msg.chat.id,
        "❌ Failed to generate insights. Please try again.\n\n" +
        "💡 Make sure you have some transactions recorded first."
      );
    }
  }

  // Export command for generating reports
  async handleExportCommand(msg, params) {
    try {
      const telegramUserId = msg.from?.id.toString();
      if (!telegramUserId) return;

      const telegramUser = await this.requireAuthentication(msg);
      if (!telegramUser) return;

      await this.bot.sendMessage(msg.chat.id,
        "📊 *Generating Transaction Report...*\n\n" +
        "Creating your personalized financial report...",
        { parse_mode: 'Markdown' }
      );

      // Parse export parameters
      let period = 'current_month';
      let category = null;

      if (params) {
        const paramLower = params.toLowerCase().trim();
        if (paramLower.includes('last month')) {
          period = 'last_month';
        } else if (paramLower.includes('this year') || paramLower.match(/^\d{4}$/)) {
          period = 'current_year';
        } else if (paramLower.includes('all')) {
          period = 'all_time';
        } else {
          // Assume it's a category filter
          category = paramLower;
        }
      }

      const report = await this.generateTransactionReport(telegramUser.user_id, period, category);
      await this.bot.sendMessage(msg.chat.id, report, { parse_mode: 'Markdown' });

    } catch (error) {
      console.error('Export command error:', error);
      await this.bot.sendMessage(msg.chat.id,
        "❌ Failed to generate report. Please try again."
      );
    }
  }

  // Settings management command
  async handleSettingsCommand(msg, params) {
    try {
      const telegramUserId = msg.from?.id.toString();
      if (!telegramUserId) return;

      const telegramUser = await this.requireAuthentication(msg);
      if (!telegramUser) return;

      if (!params) {
        // Show current settings
        await this.showUserSettings(msg, telegramUser);
        return;
      }

      // Parse setting change
      const parts = params.trim().split(' ');
      const setting = parts[0].toLowerCase();
      const value = parts[1];

      await this.updateUserSetting(msg, telegramUser, setting, value);

    } catch (error) {
      console.error('Settings command error:', error);
      await this.bot.sendMessage(msg.chat.id,
        "❌ Failed to process settings. Please try again."
      );
    }
  }

  // Sync command for data synchronization
  async handleSyncCommand(msg) {
    try {
      const telegramUserId = msg.from?.id.toString();
      if (!telegramUserId) return;

      const telegramUser = await this.requireAuthentication(msg);
      if (!telegramUser) return;

      await this.bot.sendMessage(msg.chat.id,
        "🔄 *Synchronizing with FiNManageR Web App...*\n\n" +
        "Refreshing categories, budgets, and preferences...",
        { parse_mode: 'Markdown' }
      );

      // Clear caches to force fresh data
      this.categoriesCache.clear();
      this.userInsightsCache.clear();

      // Verify data integrity
      const { data: categories } = await this.supabase
        .from('categories')
        .select('count(*)')
        .eq('user_id', telegramUser.user_id);

      const { data: transactions } = await this.supabase
        .from('transactions')
        .select('count(*)')
        .eq('user_id', telegramUser.user_id);

      const syncMessage = `
✅ *Synchronization Complete!*

*📊 Data Summary:*
• Categories: ${categories?.[0]?.count || 0} synced
• Transactions: ${transactions?.[0]?.count || 0} synced
• Cache: Refreshed
• Preferences: Updated

*🔄 What was synced:*
• Personal categories from web app
• Budget settings and limits
• Notification preferences
• Transaction history
• User preferences

*💡 All data is now up-to-date with your web app!*

Try \`/categories\` or \`/insights\` to see the latest data.
      `;

      await this.bot.sendMessage(msg.chat.id, syncMessage, { parse_mode: 'Markdown' });

    } catch (error) {
      console.error('Sync command error:', error);
      await this.bot.sendMessage(msg.chat.id,
        "❌ Failed to synchronize data. Please try again.\n\n" +
        "If the problem persists, check your internet connection."
      );
    }
  }

  // Helper method implementations
  async showAllBudgets(msg, telegramUser) {
    const { BudgetHelpers } = require('./enhanced-bot-helpers.js');
    await BudgetHelpers.showAllBudgets(this.bot, this.supabase, msg, telegramUser);
  }

  async showCategoryBudget(msg, telegramUser, category) {
    const { BudgetHelpers } = require('./enhanced-bot-helpers.js');
    await BudgetHelpers.showCategoryBudget(this.bot, this.supabase, msg, telegramUser, category);
  }

  async setBudget(msg, telegramUser, category, amount) {
    const { BudgetHelpers } = require('./enhanced-bot-helpers.js');
    await BudgetHelpers.setBudget(this.bot, this.supabase, msg, telegramUser, category, amount);
  }

  async removeBudget(msg, telegramUser, category) {
    const { BudgetHelpers } = require('./enhanced-bot-helpers.js');
    await BudgetHelpers.removeBudget(this.bot, this.supabase, msg, telegramUser, category);
  }

  async generateAIInsights(userId) {
    const { AIInsightsHelpers } = require('./enhanced-bot-helpers.js');
    return await AIInsightsHelpers.generateAIInsights(this.supabase, userId);
  }

  async generateTransactionReport(userId, period, category) {
    try {
      let dateFilter = '';
      let periodName = '';

      const now = new Date();

      switch (period) {
        case 'current_month':
          const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
          dateFilter = startOfMonth.toISOString();
          periodName = now.toLocaleString('default', { month: 'long', year: 'numeric' });
          break;
        case 'last_month':
          const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
          const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);
          dateFilter = lastMonth.toISOString();
          periodName = lastMonth.toLocaleString('default', { month: 'long', year: 'numeric' });
          break;
        case 'current_year':
          const startOfYear = new Date(now.getFullYear(), 0, 1);
          dateFilter = startOfYear.toISOString();
          periodName = now.getFullYear().toString();
          break;
        default:
          periodName = 'All Time';
      }

      // Build query
      let query = this.supabase
        .from('transactions')
        .select('*')
        .eq('user_id', userId)
        .order('date', { ascending: false });

      if (dateFilter) {
        query = query.gte('date', dateFilter);
      }

      if (category) {
        query = query.ilike('category', `%${category}%`);
      }

      const { data: transactions, error } = await query;

      if (error) throw error;

      if (!transactions || transactions.length === 0) {
        return `
📊 *Transaction Report - ${periodName}*

📝 **No transactions found** for the selected period.

*🚀 Start tracking:*
• "Spent 500 on lunch" with photo attachment
• \`/expense 500 food lunch\`
• \`/income 50000 salary\`

*💡 Tips for better reports:*
• Record transactions regularly
• Use consistent category names
• Attach receipts for better tracking
        `;
      }

      // Calculate summary statistics
      let totalIncome = 0;
      let totalExpenses = 0;
      let transactionsWithAttachments = 0;
      const categoryBreakdown = {};

      transactions.forEach(transaction => {
        if (transaction.type === 'income') {
          totalIncome += transaction.amount;
        } else {
          totalExpenses += transaction.amount;
        }

        if (transaction.attachment_url) {
          transactionsWithAttachments++;
        }

        const cat = transaction.category;
        if (!categoryBreakdown[cat]) {
          categoryBreakdown[cat] = { income: 0, expense: 0, count: 0 };
        }
        categoryBreakdown[cat][transaction.type] += transaction.amount;
        categoryBreakdown[cat].count++;
      });

      const netAmount = totalIncome - totalExpenses;
      const attachmentRate = Math.round((transactionsWithAttachments / transactions.length) * 100);

      // Generate category breakdown
      const categoryText = Object.entries(categoryBreakdown)
        .sort(([,a], [,b]) => (b.income + b.expense) - (a.income + a.expense))
        .slice(0, 5)
        .map(([category, data]) => {
          const total = data.income + data.expense;
          return `• **${category}:** ₹${total.toLocaleString()} (${data.count} transactions)`;
        })
        .join('\n');

      return `
📊 *Transaction Report - ${periodName}*

*💰 Financial Summary:*
• **Total Income:** ₹${totalIncome.toLocaleString()}
• **Total Expenses:** ₹${totalExpenses.toLocaleString()}
• **Net Amount:** ₹${netAmount.toLocaleString()} ${netAmount >= 0 ? '✅' : '⚠️'}

*📈 Transaction Details:*
• **Total Transactions:** ${transactions.length}
• **With Attachments:** ${transactionsWithAttachments} (${attachmentRate}%)
• **Average per Transaction:** ₹${Math.round((totalIncome + totalExpenses) / transactions.length).toLocaleString()}

*📂 Top Categories:*
${categoryText}

*🔗 Export Options:*
• \`/export last month\` - Previous month report
• \`/export 2024\` - Full year report
• \`/export food\` - Category-specific report

*💡 For detailed analysis, use \`/insights\` to get AI-powered recommendations.*
      `;

    } catch (error) {
      console.error('Generate transaction report error:', error);
      return `
📊 *Transaction Report*

❌ **Report Generation Failed**

Unable to generate your transaction report right now.

*🔄 Try Again:*
• Check your internet connection
• Use \`/sync\` to refresh data
• Try a different time period

*💡 Alternative:*
• Use \`/recent\` for recent transactions
• Use \`/balance\` for current summary
      `;
    }
  }

  async showUserSettings(msg, telegramUser) {
    try {
      const preferences = telegramUser.preferences || {};

      const settingsMessage = `
⚙️ *Your Bot Settings*

*🔔 Notification Preferences:*
• **Notifications:** ${preferences.notifications_enabled ? '✅ Enabled' : '❌ Disabled'}
• **Budget Alerts:** ${preferences.budget_alerts ? '✅ Enabled' : '❌ Disabled'}
• **Daily Summary:** ${preferences.daily_summary ? '✅ Enabled' : '❌ Disabled'}
• **Spending Insights:** ${preferences.spending_insights ? '✅ Enabled' : '❌ Disabled'}

*📊 Alert Thresholds:*
• **Budget Warning:** ${preferences.budget_alert_threshold || 80}%
• **Unusual Spending:** ${preferences.unusual_spending_threshold || 150}% above average

*⏰ Timing Preferences:*
• **Daily Summary Time:** ${preferences.daily_summary_time || '20:00'}
• **Reminder Frequency:** ${preferences.reminder_frequency || 'Daily'}

*🔧 Change Settings:*
• \`/settings notifications on/off\` - Toggle notifications
• \`/settings budget_alerts on/off\` - Toggle budget alerts
• \`/settings daily_summary on/off\` - Toggle daily summaries
• \`/settings budget_threshold 90\` - Set budget alert at 90%
• \`/settings summary_time 19:00\` - Set daily summary time

*💡 Recommended Settings:*
• Enable budget alerts for better financial control
• Set daily summary for regular spending awareness
• Keep notifications on for important alerts
      `;

      await this.bot.sendMessage(msg.chat.id, settingsMessage, { parse_mode: 'Markdown' });

    } catch (error) {
      console.error('Show user settings error:', error);
      await this.bot.sendMessage(msg.chat.id,
        "❌ Failed to retrieve settings. Please try again."
      );
    }
  }

  async updateUserSetting(msg, telegramUser, setting, value) {
    try {
      const preferences = telegramUser.preferences || {};
      let updateMessage = '';

      switch (setting) {
        case 'notifications':
          preferences.notifications_enabled = value === 'on' || value === 'true';
          updateMessage = `🔔 Notifications ${preferences.notifications_enabled ? 'enabled' : 'disabled'}`;
          break;
        case 'budget_alerts':
          preferences.budget_alerts = value === 'on' || value === 'true';
          updateMessage = `💰 Budget alerts ${preferences.budget_alerts ? 'enabled' : 'disabled'}`;
          break;
        case 'daily_summary':
          preferences.daily_summary = value === 'on' || value === 'true';
          updateMessage = `📊 Daily summary ${preferences.daily_summary ? 'enabled' : 'disabled'}`;
          break;
        case 'budget_threshold':
          const threshold = parseInt(value);
          if (threshold >= 50 && threshold <= 100) {
            preferences.budget_alert_threshold = threshold;
            updateMessage = `⚠️ Budget alert threshold set to ${threshold}%`;
          } else {
            await this.bot.sendMessage(msg.chat.id,
              "❌ Budget threshold must be between 50% and 100%"
            );
            return;
          }
          break;
        case 'summary_time':
          if (value.match(/^\d{2}:\d{2}$/)) {
            preferences.daily_summary_time = value;
            updateMessage = `⏰ Daily summary time set to ${value}`;
          } else {
            await this.bot.sendMessage(msg.chat.id,
              "❌ Time format should be HH:MM (e.g., 20:00)"
            );
            return;
          }
          break;
        default:
          await this.bot.sendMessage(msg.chat.id,
            "❌ Unknown setting. Use `/settings` to see available options."
          );
          return;
      }

      // Update preferences in database
      const { error } = await this.supabase
        .from('telegram_users')
        .update({ preferences: preferences })
        .eq('id', telegramUser.id);

      if (error) throw error;

      await this.bot.sendMessage(msg.chat.id,
        `✅ *Setting Updated!*\n\n${updateMessage}\n\nUse \`/settings\` to view all your preferences.`,
        { parse_mode: 'Markdown' }
      );

    } catch (error) {
      console.error('Update user setting error:', error);
      await this.bot.sendMessage(msg.chat.id,
        "❌ Failed to update setting. Please try again."
      );
    }
  }

  // Notification system methods
  async processScheduledNotifications() {
    try {
      // Get all active users with notifications enabled
      const { data: users, error } = await this.supabase
        .from('telegram_users')
        .select('*')
        .eq('is_active', true)
        .not('preferences->notifications_enabled', 'is', false);

      if (error || !users) return;

      for (const user of users) {
        const preferences = user.preferences || {};

        // Send daily summary if enabled
        if (preferences.daily_summary) {
          const summaryTime = preferences.daily_summary_time || '20:00';
          const now = new Date();
          const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;

          if (currentTime === summaryTime) {
            await this.sendDailySummary(user);
          }
        }
      }

    } catch (error) {
      console.error('Process scheduled notifications error:', error);
    }
  }

  async processBudgetAlerts() {
    try {
      // Get all users with budget alerts enabled
      const { data: users, error } = await this.supabase
        .from('telegram_users')
        .select('*')
        .eq('is_active', true)
        .not('preferences->budget_alerts', 'is', false);

      if (error || !users) return;

      for (const user of users) {
        await this.checkUserBudgetAlerts(user);
      }

    } catch (error) {
      console.error('Process budget alerts error:', error);
    }
  }

  async checkBudgetAlert(userId, category, amount, telegramUserId) {
    try {
      // Get budget for this category
      const { data: budget, error } = await this.supabase
        .from('budgets')
        .select('*')
        .eq('user_id', userId)
        .ilike('category', `%${category}%`)
        .single();

      if (error || !budget) return;

      // Get current month spending for this category
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();
      const startOfMonth = new Date(currentYear, currentMonth, 1).toISOString();

      const { data: spending } = await this.supabase
        .from('transactions')
        .select('amount')
        .eq('user_id', userId)
        .eq('type', 'expense')
        .ilike('category', `%${category}%`)
        .gte('date', startOfMonth);

      const totalSpent = spending?.reduce((sum, t) => sum + t.amount, 0) || 0;
      const percentage = Math.round((totalSpent / budget.amount) * 100);

      // Send alert if threshold exceeded
      const threshold = 80; // Default threshold
      if (percentage >= threshold && percentage < 100) {
        const alertMessage = `
⚠️ *Budget Alert - ${category.toUpperCase()}*

You've spent **${percentage}%** of your ${category} budget this month.

*📊 Budget Status:*
• Budget: ₹${budget.amount.toLocaleString()}
• Spent: ₹${totalSpent.toLocaleString()}
• Remaining: ₹${(budget.amount - totalSpent).toLocaleString()}

*💡 Consider:*
• Review recent ${category} expenses
• Look for ways to optimize spending
• Adjust budget if needed: \`/budget ${category} ${Math.round(budget.amount * 1.2)}\`

Use \`/budget ${category}\` for detailed analysis.
        `;

        await this.bot.sendMessage(telegramUserId, alertMessage, { parse_mode: 'Markdown' });
      } else if (percentage >= 100) {
        const overBudgetMessage = `
🚨 *Budget Exceeded - ${category.toUpperCase()}*

You've **exceeded** your ${category} budget by ₹${(totalSpent - budget.amount).toLocaleString()}!

*📊 Budget Status:*
• Budget: ₹${budget.amount.toLocaleString()}
• Spent: ₹${totalSpent.toLocaleString()} (${percentage}%)
• Over by: ₹${(totalSpent - budget.amount).toLocaleString()}

*🎯 Action Items:*
• Review and categorize recent expenses
• Consider increasing budget: \`/budget ${category} ${Math.round(totalSpent * 1.1)}\`
• Look for cost-cutting opportunities
• Use \`/insights\` for spending analysis

Stay on track with your financial goals! 💪
        `;

        await this.bot.sendMessage(telegramUserId, overBudgetMessage, { parse_mode: 'Markdown' });
      }

    } catch (error) {
      console.error('Check budget alert error:', error);
    }
  }

  async sendDailySummary(user) {
    try {
      // Get today's transactions
      const today = new Date();
      const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate()).toISOString();

      const { data: todayTransactions } = await this.supabase
        .from('transactions')
        .select('*')
        .eq('user_id', user.user_id)
        .gte('date', startOfDay);

      if (!todayTransactions || todayTransactions.length === 0) {
        // No transactions today
        const reminderMessage = `
🌅 *Daily Financial Summary*

📝 **No transactions recorded today**

*💡 Don't forget to:*
• Log your expenses: "Spent 500 on lunch"
• Attach receipts for better tracking
• Check your budgets: \`/budget\`
• Review spending patterns: \`/insights\`

Keep up your financial tracking habits! 💪
        `;

        await this.bot.sendMessage(user.telegram_id, reminderMessage, { parse_mode: 'Markdown' });
        return;
      }

      // Calculate daily summary
      let dailyIncome = 0;
      let dailyExpenses = 0;
      let transactionsWithAttachments = 0;
      const categorySpending = {};

      todayTransactions.forEach(transaction => {
        if (transaction.type === 'income') {
          dailyIncome += transaction.amount;
        } else {
          dailyExpenses += transaction.amount;
          categorySpending[transaction.category] = (categorySpending[transaction.category] || 0) + transaction.amount;
        }

        if (transaction.attachment_url) {
          transactionsWithAttachments++;
        }
      });

      const netAmount = dailyIncome - dailyExpenses;
      const attachmentRate = Math.round((transactionsWithAttachments / todayTransactions.length) * 100);

      // Top spending category
      const topCategory = Object.entries(categorySpending)
        .sort(([,a], [,b]) => b - a)[0];

      const summaryMessage = `
🌅 *Daily Financial Summary - ${today.toLocaleDateString()}*

*💰 Today's Activity:*
• **Income:** ₹${dailyIncome.toLocaleString()}
• **Expenses:** ₹${dailyExpenses.toLocaleString()}
• **Net:** ₹${netAmount.toLocaleString()} ${netAmount >= 0 ? '✅' : '⚠️'}

*📊 Transaction Details:*
• **Total Transactions:** ${todayTransactions.length}
• **With Receipts:** ${transactionsWithAttachments} (${attachmentRate}%)
${topCategory ? `• **Top Category:** ${topCategory[0]} (₹${topCategory[1].toLocaleString()})` : ''}

*🎯 Tomorrow's Goals:*
• Continue tracking all expenses
• Attach receipts for better records
• Stay within your budgets
• Review spending patterns regularly

*Quick Actions:* \`/balance\` • \`/insights\` • \`/budget\`

Keep up the great financial habits! 🌟
      `;

      await this.bot.sendMessage(user.telegram_id, summaryMessage, { parse_mode: 'Markdown' });

    } catch (error) {
      console.error('Send daily summary error:', error);
    }
  }

  // NLP and utility methods (from original optimized bot)
  parseNaturalLanguage(message) {
    const lowerMessage = message.toLowerCase();

    // Extract amount
    const amount = this.extractAmount(lowerMessage);
    if (!amount) {
      return { success: false, error: 'No amount found' };
    }

    // Determine transaction type
    const type = this.determineTransactionType(lowerMessage);
    if (!type) {
      return { success: false, error: 'Cannot determine transaction type' };
    }

    // Extract category
    const category = this.extractCategory(lowerMessage, type);
    if (!category) {
      return { success: false, error: 'Cannot determine category' };
    }

    // Extract description
    const description = this.extractDescription(message, amount, category);

    // Calculate confidence
    const confidence = this.calculateConfidence(lowerMessage, amount, type, category);

    return {
      success: true,
      confidence: confidence,
      transaction: {
        amount: amount,
        type: type,
        category: category,
        description: description
      }
    };
  }

  extractAmount(message) {
    // Look for various amount patterns
    const patterns = [
      /(?:₹|rs\.?|rupees?)\s*(\d+(?:,\d{3})*(?:\.\d{2})?)/i,
      /(\d+(?:,\d{3})*(?:\.\d{2})?)\s*(?:₹|rs\.?|rupees?)/i,
      /(\d+(?:,\d{3})*(?:\.\d{2})?)/
    ];

    for (const pattern of patterns) {
      const match = message.match(pattern);
      if (match) {
        const amount = parseFloat(match[1].replace(/,/g, ''));
        if (amount > 0 && amount < 10000000) { // Reasonable limits
          return amount;
        }
      }
    }

    return null;
  }

  determineTransactionType(message) {
    const expenseKeywords = ['spent', 'paid', 'bought', 'purchase', 'expense', 'cost', 'bill'];
    const incomeKeywords = ['earned', 'received', 'income', 'salary', 'got', 'profit'];

    const hasExpenseKeyword = expenseKeywords.some(keyword => message.includes(keyword));
    const hasIncomeKeyword = incomeKeywords.some(keyword => message.includes(keyword));

    if (hasExpenseKeyword && !hasIncomeKeyword) return 'expense';
    if (hasIncomeKeyword && !hasExpenseKeyword) return 'income';

    // Default to expense if ambiguous
    return 'expense';
  }

  extractCategory(message, type) {
    const expenseCategories = [
      'food', 'groceries', 'transport', 'transportation', 'shopping', 'entertainment',
      'bills', 'utilities', 'healthcare', 'education', 'travel', 'rent', 'fuel',
      'coffee', 'lunch', 'dinner', 'breakfast', 'snacks', 'restaurant'
    ];

    const incomeCategories = [
      'salary', 'freelance', 'business', 'investment', 'rental', 'interest',
      'dividend', 'gift', 'refund', 'bonus', 'commission'
    ];

    const categories = type === 'expense' ? expenseCategories : incomeCategories;

    for (const category of categories) {
      if (message.includes(category)) {
        return category;
      }
    }

    return type === 'expense' ? 'other' : 'other';
  }

  extractDescription(message, amount, category) {
    // Remove amount and common words to get description
    let description = message
      .replace(/₹|rs\.?|rupees?/gi, '')
      .replace(new RegExp(amount.toString(), 'g'), '')
      .replace(/spent|paid|bought|earned|received/gi, '')
      .replace(/on|for|from/gi, '')
      .replace(new RegExp(category, 'gi'), '')
      .trim();

    return description || `${category} transaction`;
  }

  calculateConfidence(message, amount, type, category) {
    let confidence = 50; // Base confidence

    // Amount found
    if (amount) confidence += 20;

    // Type keywords found
    const expenseKeywords = ['spent', 'paid', 'bought'];
    const incomeKeywords = ['earned', 'received'];
    const keywords = type === 'expense' ? expenseKeywords : incomeKeywords;

    if (keywords.some(keyword => message.includes(keyword))) {
      confidence += 20;
    }

    // Category found
    if (category && category !== 'other') confidence += 15;

    // Message structure
    if (message.includes('on') || message.includes('for') || message.includes('from')) {
      confidence += 10;
    }

    return Math.min(confidence, 95); // Cap at 95%
  }

  getNLPSuggestions(message) {
    const suggestions = [];

    if (!this.extractAmount(message)) {
      suggestions.push("💡 Include the amount: 'Spent ₹500 on lunch'");
    }

    if (!message.match(/spent|paid|bought|earned|received/i)) {
      suggestions.push("💡 Use action words: 'Spent', 'Paid', 'Earned', 'Received'");
    }

    if (!message.match(/on|for|from/i)) {
      suggestions.push("💡 Use connecting words: 'on', 'for', 'from'");
    }

    return suggestions;
  }

  async requireAuthentication(msg) {
    const telegramUserId = msg.from?.id.toString();
    if (!telegramUserId) return null;

    const { data: telegramUser } = await this.supabase
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', telegramUserId)
      .eq('is_active', true)
      .single();

    if (!telegramUser) {
      await this.bot.sendMessage(msg.chat.id,
        "🔗 *Please link your account first*\n\n" +
        "To use enhanced features, you need to link your Telegram account to FiNManageR.\n\n" +
        "*Steps:*\n" +
        "1. Visit: https://finmanager.netlify.app\n" +
        "2. Go to Settings → Telegram Integration\n" +
        "3. Generate an 8-digit code\n" +
        "4. Use: `/link <your-code>`\n\n" +
        "🚀 Once linked, you'll get:\n" +
        "• 📸 Receipt attachment support\n" +
        "• 🧠 AI financial insights\n" +
        "• 🔔 Smart notifications\n" +
        "• 📊 Advanced analytics",
        { parse_mode: 'Markdown' }
      );
      return null;
    }

    return telegramUser;
  }
}

// Start the enhanced bot only if this file is run directly
if (require.main === module) {
  const bot = new EnhancedTelegramBot(process.env.TELEGRAM_BOT_TOKEN);
  console.log('🚀 Enhanced Telegram Bot is running...');
}

module.exports = EnhancedTelegramBot;
