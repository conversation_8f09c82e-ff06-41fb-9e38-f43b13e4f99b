{"name": "finmanager-telegram-bot", "version": "1.0.0", "description": "FiNManageR Telegram Bot - Enterprise Financial Assistant for Render.com", "main": "enterprise-bot.js", "type": "commonjs", "scripts": {"start": "node enterprise-bot.js", "dev": "nodemon enterprise-bot.js", "simple": "node index.js", "test": "echo 'No tests specified'", "health": "curl -f http://localhost:3001/health || exit 1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "dependencies": {"@google-cloud/storage": "^7.16.0", "@supabase/supabase-js": "^2.49.4", "axios": "^1.6.8", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.18.2", "helmet": "^7.1.0", "joi": "^17.12.2", "morgan": "^1.10.0", "multer": "^2.0.1", "node-telegram-bot-api": "^0.66.0", "sharp": "^0.33.2", "tesseract.js": "^5.0.5", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.0.3"}, "overrides": {"tough-cookie": "^4.1.3", "request": "^2.88.2"}, "keywords": ["telegram", "bot", "finance", "fintech", "expense-tracker", "budget", "render", "deployment"], "author": "FiNManageR Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/finmanager-bot.git"}}