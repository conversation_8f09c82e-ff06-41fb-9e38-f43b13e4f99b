/**
 * Helper methods for Enhanced Telegram Bot
 * Contains AI insights, budget management, notifications, and utility functions
 */

// Budget Management Helper Methods
const BudgetHelpers = {
  async showAllBudgets(bot, supabase, msg, telegramUser) {
    try {
      // Get user's budgets
      const { data: budgets, error } = await supabase
        .from('budgets')
        .select('*')
        .eq('user_id', telegramUser.user_id);

      if (error) throw error;

      if (!budgets || budgets.length === 0) {
        await bot.sendMessage(msg.chat.id,
          "💰 *No Budgets Set*\n\n" +
          "You haven't set any spending budgets yet.\n\n" +
          "*🎯 Get Started:*\n" +
          "• `/budget food 5000` - Set food budget to ₹5000\n" +
          "• `/budget transport 2000` - Set transport budget\n" +
          "• `/budget entertainment 3000` - Set entertainment budget\n\n" +
          "*💡 Benefits of Setting Budgets:*\n" +
          "• Get automatic spending alerts\n" +
          "• Track progress towards financial goals\n" +
          "• Receive AI-powered optimization tips\n" +
          "• Better financial discipline and control",
          { parse_mode: 'Markdown' }
        );
        return;
      }

      // Get current month spending for each budget category
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();
      const startOfMonth = new Date(currentYear, currentMonth, 1).toISOString();

      let budgetMessage = "💰 *Your Spending Budgets*\n\n";

      for (const budget of budgets) {
        // Get spending for this category this month
        const { data: spending } = await supabase
          .from('transactions')
          .select('amount')
          .eq('user_id', telegramUser.user_id)
          .eq('type', 'expense')
          .ilike('category', `%${budget.category}%`)
          .gte('date', startOfMonth);

        const totalSpent = spending?.reduce((sum, t) => sum + t.amount, 0) || 0;
        const percentage = Math.round((totalSpent / budget.amount) * 100);
        const remaining = budget.amount - totalSpent;

        let statusEmoji = '✅';
        let statusText = 'On Track';
        
        if (percentage >= 100) {
          statusEmoji = '🚨';
          statusText = 'Over Budget';
        } else if (percentage >= 80) {
          statusEmoji = '⚠️';
          statusText = 'Near Limit';
        } else if (percentage >= 60) {
          statusEmoji = '🟡';
          statusText = 'Moderate';
        }

        budgetMessage += `${statusEmoji} **${budget.category.toUpperCase()}**\n`;
        budgetMessage += `   Budget: ₹${budget.amount.toLocaleString()}\n`;
        budgetMessage += `   Spent: ₹${totalSpent.toLocaleString()} (${percentage}%)\n`;
        budgetMessage += `   Remaining: ₹${remaining.toLocaleString()}\n`;
        budgetMessage += `   Status: ${statusText}\n\n`;
      }

      budgetMessage += "*🔧 Budget Management:*\n";
      budgetMessage += "• `/budget food 6000` - Update food budget\n";
      budgetMessage += "• `/budget food reset` - Remove food budget\n";
      budgetMessage += "• `/budget transport` - View transport budget details\n\n";
      budgetMessage += "💡 *Tip:* Set realistic budgets based on your spending history. Use `/insights` for recommendations.";

      await bot.sendMessage(msg.chat.id, budgetMessage, { parse_mode: 'Markdown' });

    } catch (error) {
      console.error('Show all budgets error:', error);
      await bot.sendMessage(msg.chat.id,
        "❌ Failed to retrieve budgets. Please try again."
      );
    }
  },

  async showCategoryBudget(bot, supabase, msg, telegramUser, category) {
    try {
      // Get specific budget
      const { data: budget, error } = await supabase
        .from('budgets')
        .select('*')
        .eq('user_id', telegramUser.user_id)
        .ilike('category', `%${category}%`)
        .single();

      if (error || !budget) {
        await bot.sendMessage(msg.chat.id,
          `💰 *No Budget Set for "${category}"*\n\n` +
          `Set a budget to start tracking your ${category} spending:\n\n` +
          `\`/budget ${category} 5000\` - Set ₹5000 monthly budget\n\n` +
          "💡 *Benefits:*\n" +
          "• Automatic spending alerts\n" +
          "• Progress tracking\n" +
          "• AI optimization suggestions",
          { parse_mode: 'Markdown' }
        );
        return;
      }

      // Get spending history for this category
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();
      const startOfMonth = new Date(currentYear, currentMonth, 1).toISOString();
      const startOfLastMonth = new Date(currentYear, currentMonth - 1, 1).toISOString();
      const endOfLastMonth = new Date(currentYear, currentMonth, 0).toISOString();

      // Current month spending
      const { data: currentSpending } = await supabase
        .from('transactions')
        .select('amount, date')
        .eq('user_id', telegramUser.user_id)
        .eq('type', 'expense')
        .ilike('category', `%${category}%`)
        .gte('date', startOfMonth);

      // Last month spending
      const { data: lastMonthSpending } = await supabase
        .from('transactions')
        .select('amount')
        .eq('user_id', telegramUser.user_id)
        .eq('type', 'expense')
        .ilike('category', `%${category}%`)
        .gte('date', startOfLastMonth)
        .lte('date', endOfLastMonth);

      const currentTotal = currentSpending?.reduce((sum, t) => sum + t.amount, 0) || 0;
      const lastMonthTotal = lastMonthSpending?.reduce((sum, t) => sum + t.amount, 0) || 0;
      const percentage = Math.round((currentTotal / budget.amount) * 100);
      const remaining = budget.amount - currentTotal;
      const trend = currentTotal > lastMonthTotal ? '📈 Higher' : 
                   currentTotal < lastMonthTotal ? '📉 Lower' : '➡️ Same';

      let statusEmoji = '✅';
      let statusText = 'On Track';
      let recommendation = 'Great job staying within budget!';
      
      if (percentage >= 100) {
        statusEmoji = '🚨';
        statusText = 'Over Budget';
        recommendation = 'Consider reducing spending in this category or adjusting your budget.';
      } else if (percentage >= 80) {
        statusEmoji = '⚠️';
        statusText = 'Near Limit';
        recommendation = 'You\'re approaching your budget limit. Monitor spending carefully.';
      } else if (percentage >= 60) {
        statusEmoji = '🟡';
        statusText = 'Moderate Usage';
        recommendation = 'Good progress. You have room for more spending if needed.';
      }

      const budgetDetails = `
${statusEmoji} *${category.toUpperCase()} Budget Details*

*📊 Current Month (${new Date().toLocaleString('default', { month: 'long' })}):*
• Budget: ₹${budget.amount.toLocaleString()}
• Spent: ₹${currentTotal.toLocaleString()} (${percentage}%)
• Remaining: ₹${remaining.toLocaleString()}
• Status: **${statusText}**

*📈 Comparison:*
• Last Month: ₹${lastMonthTotal.toLocaleString()}
• Trend: ${trend}
• Transactions: ${currentSpending?.length || 0} this month

*🎯 AI Recommendation:*
${recommendation}

*🔧 Quick Actions:*
• \`/budget ${category} ${Math.round(budget.amount * 1.2)}\` - Increase budget by 20%
• \`/budget ${category} reset\` - Remove this budget
• \`/recent ${category}\` - View ${category} transactions
• \`/insights\` - Get detailed spending analysis
      `;

      await bot.sendMessage(msg.chat.id, budgetDetails, { parse_mode: 'Markdown' });

    } catch (error) {
      console.error('Show category budget error:', error);
      await bot.sendMessage(msg.chat.id,
        "❌ Failed to retrieve budget details. Please try again."
      );
    }
  },

  async setBudget(bot, supabase, msg, telegramUser, category, amount) {
    try {
      // Check if budget already exists
      const { data: existingBudget } = await supabase
        .from('budgets')
        .select('*')
        .eq('user_id', telegramUser.user_id)
        .ilike('category', `%${category}%`)
        .single();

      if (existingBudget) {
        // Update existing budget
        const { error } = await supabase
          .from('budgets')
          .update({
            amount: amount,
            updated_at: new Date().toISOString()
          })
          .eq('id', existingBudget.id);

        if (error) throw error;

        await bot.sendMessage(msg.chat.id,
          `✅ *Budget Updated Successfully!*\n\n` +
          `**${category.toUpperCase()}** budget updated to ₹${amount.toLocaleString()}\n\n` +
          `🔔 You'll receive alerts when you reach 80% of this budget.\n\n` +
          `*Quick Actions:*\n` +
          `• \`/budget ${category}\` - View budget details\n` +
          `• \`/recent ${category}\` - View ${category} transactions\n` +
          `• \`/insights\` - Get spending analysis`,
          { parse_mode: 'Markdown' }
        );
      } else {
        // Create new budget
        const { error } = await supabase
          .from('budgets')
          .insert({
            user_id: telegramUser.user_id,
            category: category,
            amount: amount,
            period: 'monthly',
            created_at: new Date().toISOString()
          });

        if (error) throw error;

        await bot.sendMessage(msg.chat.id,
          `🎉 *New Budget Created!*\n\n` +
          `**${category.toUpperCase()}** budget set to ₹${amount.toLocaleString()} per month\n\n` +
          `🔔 **Smart Alerts Enabled:**\n` +
          `• 80% threshold: ₹${Math.round(amount * 0.8).toLocaleString()}\n` +
          `• 100% threshold: ₹${amount.toLocaleString()}\n\n` +
          `*💡 Pro Tips:*\n` +
          `• Track progress with \`/budget ${category}\`\n` +
          `• View spending with \`/recent ${category}\`\n` +
          `• Get AI insights with \`/insights\`\n` +
          `• Attach receipts to transactions for better tracking`,
          { parse_mode: 'Markdown' }
        );
      }

    } catch (error) {
      console.error('Set budget error:', error);
      await bot.sendMessage(msg.chat.id,
        "❌ Failed to set budget. Please try again."
      );
    }
  },

  async removeBudget(bot, supabase, msg, telegramUser, category) {
    try {
      const { data: budget, error: fetchError } = await supabase
        .from('budgets')
        .select('*')
        .eq('user_id', telegramUser.user_id)
        .ilike('category', `%${category}%`)
        .single();

      if (fetchError || !budget) {
        await bot.sendMessage(msg.chat.id,
          `❌ No budget found for "${category}".\n\n` +
          "Use `/budget` to see all your budgets."
        );
        return;
      }

      const { error } = await supabase
        .from('budgets')
        .delete()
        .eq('id', budget.id);

      if (error) throw error;

      await bot.sendMessage(msg.chat.id,
        `✅ *Budget Removed Successfully!*\n\n` +
        `**${category.toUpperCase()}** budget (₹${budget.amount.toLocaleString()}) has been removed.\n\n` +
        `🔔 You will no longer receive spending alerts for this category.\n\n` +
        `*Want to set a new budget?*\n` +
        `Use: \`/budget ${category} <amount>\``,
        { parse_mode: 'Markdown' }
      );

    } catch (error) {
      console.error('Remove budget error:', error);
      await bot.sendMessage(msg.chat.id,
        "❌ Failed to remove budget. Please try again."
      );
    }
  }
};

// AI Insights Helper Methods
const AIInsightsHelpers = {
  async generateAIInsights(supabase, userId) {
    try {
      // Get user's transaction data
      const { data: transactions, error } = await supabase
        .from('transactions')
        .select('*')
        .eq('user_id', userId)
        .order('date', { ascending: false })
        .limit(100); // Analyze last 100 transactions

      if (error) throw error;

      if (!transactions || transactions.length === 0) {
        return `
🧠 *AI Financial Insights*

📊 **No Data Available**

You don't have enough transaction data for AI analysis yet.

*🚀 Get Started:*
• Record at least 10 transactions
• Include various categories (food, transport, etc.)
• Attach receipts for better insights
• Set budgets for spending categories

*💡 Once you have more data, I'll provide:*
• Spending pattern analysis
• Budget optimization suggestions
• Unusual transaction detection
• Personalized saving recommendations
• Monthly financial health reports

Start tracking: "Spent 500 on lunch" 📸
        `;
      }

      // Analyze spending patterns
      const insights = this.analyzeSpendingPatterns(transactions);
      const recommendations = this.generateRecommendations(transactions);
      const trends = this.analyzeTrends(transactions);
      const budgetSuggestions = this.generateBudgetSuggestions(transactions);

      return `
🧠 *AI Financial Insights*

${insights}

${trends}

${recommendations}

${budgetSuggestions}

*📊 Analysis based on your last ${transactions.length} transactions*
*🔄 Updated every 30 minutes • Use \`/sync\` to refresh*
      `;

    } catch (error) {
      console.error('Generate AI insights error:', error);
      return `
🧠 *AI Financial Insights*

❌ **Analysis Temporarily Unavailable**

I'm having trouble analyzing your financial data right now.

*🔄 Try Again:*
• Use \`/sync\` to refresh your data
• Check your internet connection
• Try again in a few minutes

*💡 In the meantime:*
• Use \`/balance\` for current financial summary
• Use \`/recent\` to review transactions
• Use \`/budget\` to manage spending limits
      `;
    }
  },

  analyzeSpendingPatterns(transactions) {
    // Calculate category-wise spending
    const categorySpending = {};
    const monthlySpending = {};
    let totalExpenses = 0;
    let totalIncome = 0;
    let transactionsWithAttachments = 0;

    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();

    transactions.forEach(transaction => {
      const transactionDate = new Date(transaction.date);
      const monthKey = `${transactionDate.getFullYear()}-${transactionDate.getMonth()}`;

      if (transaction.type === 'expense') {
        totalExpenses += transaction.amount;
        categorySpending[transaction.category] = (categorySpending[transaction.category] || 0) + transaction.amount;

        if (transactionDate.getMonth() === currentMonth && transactionDate.getFullYear() === currentYear) {
          monthlySpending[transaction.category] = (monthlySpending[transaction.category] || 0) + transaction.amount;
        }
      } else {
        totalIncome += transaction.amount;
      }

      if (transaction.attachment_url) {
        transactionsWithAttachments++;
      }
    });

    // Find top spending categories
    const topCategories = Object.entries(categorySpending)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3);

    const attachmentRate = Math.round((transactionsWithAttachments / transactions.length) * 100);
    const savingsRate = totalIncome > 0 ? Math.round(((totalIncome - totalExpenses) / totalIncome) * 100) : 0;

    let patternsText = "*📊 Spending Patterns:*\n";

    if (topCategories.length > 0) {
      patternsText += `• **Top Category:** ${topCategories[0][0]} (₹${topCategories[0][1].toLocaleString()})\n`;
      if (topCategories.length > 1) {
        patternsText += `• **Second:** ${topCategories[1][0]} (₹${topCategories[1][1].toLocaleString()})\n`;
      }
      if (topCategories.length > 2) {
        patternsText += `• **Third:** ${topCategories[2][0]} (₹${topCategories[2][1].toLocaleString()})\n`;
      }
    }

    patternsText += `• **Savings Rate:** ${savingsRate}% ${savingsRate > 20 ? '✅' : savingsRate > 10 ? '🟡' : '🚨'}\n`;
    patternsText += `• **Receipt Tracking:** ${attachmentRate}% ${attachmentRate > 70 ? '✅' : attachmentRate > 40 ? '🟡' : '📸'}\n`;

    return patternsText;
  },

  analyzeTrends(transactions) {
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    const lastMonth = currentMonth === 0 ? 11 : currentMonth - 1;
    const lastMonthYear = currentMonth === 0 ? currentYear - 1 : currentYear;

    let currentMonthSpending = 0;
    let lastMonthSpending = 0;
    let currentMonthTransactions = 0;
    let lastMonthTransactions = 0;

    transactions.forEach(transaction => {
      if (transaction.type === 'expense') {
        const transactionDate = new Date(transaction.date);

        if (transactionDate.getMonth() === currentMonth && transactionDate.getFullYear() === currentYear) {
          currentMonthSpending += transaction.amount;
          currentMonthTransactions++;
        } else if (transactionDate.getMonth() === lastMonth && transactionDate.getFullYear() === lastMonthYear) {
          lastMonthSpending += transaction.amount;
          lastMonthTransactions++;
        }
      }
    });

    const spendingChange = lastMonthSpending > 0 ?
      Math.round(((currentMonthSpending - lastMonthSpending) / lastMonthSpending) * 100) : 0;

    const transactionChange = lastMonthTransactions > 0 ?
      Math.round(((currentMonthTransactions - lastMonthTransactions) / lastMonthTransactions) * 100) : 0;

    let trendEmoji = '➡️';
    let trendText = 'stable';

    if (spendingChange > 10) {
      trendEmoji = '📈';
      trendText = 'increasing';
    } else if (spendingChange < -10) {
      trendEmoji = '📉';
      trendText = 'decreasing';
    }

    return `
*📈 Monthly Trends:*
• **Spending:** ${trendEmoji} ${Math.abs(spendingChange)}% ${trendText} vs last month
• **This Month:** ₹${currentMonthSpending.toLocaleString()} (${currentMonthTransactions} transactions)
• **Last Month:** ₹${lastMonthSpending.toLocaleString()} (${lastMonthTransactions} transactions)
    `;
  },

  generateRecommendations(transactions) {
    const recommendations = [];

    // Analyze attachment usage
    const transactionsWithAttachments = transactions.filter(t => t.attachment_url).length;
    const attachmentRate = Math.round((transactionsWithAttachments / transactions.length) * 100);

    if (attachmentRate < 50) {
      recommendations.push("📸 **Attach more receipts** - Only " + attachmentRate + "% of transactions have receipts. Better record-keeping helps with budgeting!");
    }

    // Analyze spending frequency
    const expenseTransactions = transactions.filter(t => t.type === 'expense');
    const avgDailyTransactions = expenseTransactions.length / 30; // Approximate

    if (avgDailyTransactions > 5) {
      recommendations.push("🎯 **Consider bulk purchases** - You're making " + Math.round(avgDailyTransactions) + " transactions daily. Consolidating could save money!");
    }

    // Analyze category distribution
    const categorySpending = {};
    expenseTransactions.forEach(t => {
      categorySpending[t.category] = (categorySpending[t.category] || 0) + t.amount;
    });

    const totalExpenses = Object.values(categorySpending).reduce((sum, amount) => sum + amount, 0);
    const topCategory = Object.entries(categorySpending).sort(([,a], [,b]) => b - a)[0];

    if (topCategory && (topCategory[1] / totalExpenses) > 0.4) {
      recommendations.push(`💡 **Diversify spending** - ${Math.round((topCategory[1] / totalExpenses) * 100)}% goes to ${topCategory[0]}. Consider if this aligns with your priorities.`);
    }

    // Default recommendations if none generated
    if (recommendations.length === 0) {
      recommendations.push("✅ **Great financial habits!** Your spending patterns look healthy. Keep tracking and budgeting!");
      recommendations.push("🎯 **Set budgets** - Use `/budget category amount` to set spending limits and get automatic alerts.");
    }

    return "*🎯 AI Recommendations:*\n• " + recommendations.join('\n• ');
  },

  generateBudgetSuggestions(transactions) {
    const categorySpending = {};
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();

    // Calculate current month spending by category
    transactions.forEach(transaction => {
      if (transaction.type === 'expense') {
        const transactionDate = new Date(transaction.date);
        if (transactionDate.getMonth() === currentMonth && transactionDate.getFullYear() === currentYear) {
          categorySpending[transaction.category] = (categorySpending[transaction.category] || 0) + transaction.amount;
        }
      }
    });

    const suggestions = Object.entries(categorySpending)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([category, amount]) => {
        const suggestedBudget = Math.round(amount * 1.2); // 20% buffer
        return `• **${category}:** ₹${suggestedBudget.toLocaleString()} (\`/budget ${category} ${suggestedBudget}\`)`;
      });

    if (suggestions.length === 0) {
      return "*💰 Budget Suggestions:*\nStart recording expenses to get personalized budget recommendations!";
    }

    return "*💰 Suggested Monthly Budgets:*\n" + suggestions.join('\n');
  }
};

module.exports = { BudgetHelpers, AIInsightsHelpers };
