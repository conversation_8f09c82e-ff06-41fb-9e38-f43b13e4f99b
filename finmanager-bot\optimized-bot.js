require('dotenv').config();
const TelegramBot = require('node-telegram-bot-api');
const { createClient } = require('@supabase/supabase-js');

/**
 * Optimized Telegram Bot for FiNManageR
 * 
 * This version focuses on:
 * - Fast initialization
 * - Efficient database queries
 * - Proper NLP functionality
 * - Minimal service dependencies
 * - Quick response times
 */
class OptimizedTelegramBot {
  constructor(token) {
    console.log('🚀 Initializing Optimized Telegram Bot...');
    
    this.bot = new TelegramBot(token, { polling: true });
    this.supabase = this.initializeSupabase();
    
    // Cache for categories to reduce database queries
    this.categoriesCache = new Map();
    this.cacheExpiry = 5 * 60 * 1000; // 5 minutes
    
    // Pending transactions for confirmation
    this.pendingTransactions = new Map();
    
    console.log('🔧 Setting up commands...');
    this.setupCommands();
    
    console.log('🔧 Setting up event handlers...');
    this.setupEventHandlers();
    
    console.log('✅ Optimized Telegram Bot ready!');
  }

  initializeSupabase() {
    return createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        },
        db: {
          schema: 'public'
        }
      }
    );
  }

  setupCommands() {
    // Start command
    this.bot.onText(/\/start/, async (msg) => {
      const welcomeMessage = `
🎉 *Welcome to FiNManageR Bot!*

Your intelligent personal finance assistant is here to help you take control of your money! 💰

*✨ What I can do for you:*
• 📊 Track your expenses and income automatically
• 🤖 Understand natural language - just tell me what you spent!
• 📈 Provide personalized financial insights
• 🔒 Keep your data secure with enterprise-grade protection

*🚀 Quick Start:*
1. Link your account: \`/link <8-digit-code>\`
2. Start tracking: "Spent 500 on lunch" or \`/expense 500 food lunch\`
3. Check your data: \`/balance\` or \`/recent\`

*📱 Available Commands:*
• \`/help\` - Show all commands
• \`/link <code>\` - Link your FiNManageR account
• \`/categories\` - View your personal categories
• \`/balance\` - Check your current balance
• \`/recent\` - View your latest transactions

*🌟 Smart Features:*
• 💬 **Natural Language**: Just say "Spent 500 on lunch" - I'll understand!
• ⚡ **Quick Commands**: Fast expense logging with simple commands
• 🧠 **Smart Analytics**: Discover your spending patterns and trends

Ready to take control of your finances? Let's get started! 🚀

Get your linking code from: https://finmanager.netlify.app → Settings → Telegram Integration
      `;
      
      await this.bot.sendMessage(msg.chat.id, welcomeMessage, { parse_mode: 'Markdown' });
    });

    // Help command
    this.bot.onText(/\/help/, async (msg) => {
      const helpMessage = `
🤖 *FiNManageR Bot Commands*

*🔗 Account Management:*
• \`/link <8-digit-code>\` - Link your FiNManageR account
• \`/status\` - Check your account status

*💰 Transaction Commands:*
• \`/expense <amount> <category> <description>\` - Log expense
• \`/income <amount> <category> <description>\` - Log income
• \`/balance\` - Check your current balance
• \`/recent\` - View your latest transactions
• \`/categories\` - View your personal expense & income categories

*🌟 Smart Features:*
• 💬 **Natural Language:** Just say "Spent 500 on lunch" - I'll understand!
• ⚡ **Quick Commands:** Fast expense logging with simple commands
• 🧠 **Smart Analytics:** Discover your spending patterns and trends

*💡 Pro Tips:*
• Use natural language - I understand how you naturally speak about money
• Try quick commands like \`/expense 500 food lunch\` for fastest entry
• Check your categories regularly to see what's available

Need more help? Visit the FiNManageR web app for detailed guides! 🌐
      `;
      
      await this.bot.sendMessage(msg.chat.id, helpMessage, { parse_mode: 'Markdown' });
    });

    // Link command
    this.bot.onText(/\/link (.+)/, async (msg, match) => {
      await this.handleLinkCommand(msg, match[1]);
    });

    // Categories command
    this.bot.onText(/\/categories/, async (msg) => {
      await this.handleCategoriesCommand(msg);
    });

    // Balance command
    this.bot.onText(/\/balance/, async (msg) => {
      await this.handleBalanceCommand(msg);
    });

    // Recent command
    this.bot.onText(/\/recent/, async (msg) => {
      await this.handleRecentCommand(msg);
    });

    // Expense command
    this.bot.onText(/\/expense (.+)/, async (msg, match) => {
      await this.handleExpenseCommand(msg, match[1]);
    });

    // Income command
    this.bot.onText(/\/income (.+)/, async (msg, match) => {
      await this.handleIncomeCommand(msg, match[1]);
    });

    // Status command
    this.bot.onText(/\/status/, async (msg) => {
      await this.handleStatusCommand(msg);
    });
  }

  setupEventHandlers() {
    // Handle non-command messages with NLP
    this.bot.on('message', async (msg) => {
      if (msg.text && !msg.text.startsWith('/')) {
        await this.handleNaturalLanguageMessage(msg);
      }
    });

    // Handle callback queries for transaction confirmations
    this.bot.on('callback_query', async (query) => {
      await this.handleCallbackQuery(query);
    });

    // Error handling
    this.bot.on('error', (error) => {
      console.error('Bot error:', error);
    });

    this.bot.on('polling_error', (error) => {
      console.error('Polling error:', error);
    });
  }

  async handleLinkCommand(msg, code) {
    try {
      const telegramUserId = msg.from?.id.toString();
      if (!telegramUserId) return;

      if (!code || code.length !== 8) {
        await this.bot.sendMessage(msg.chat.id,
          "❌ Invalid code format. Please provide an 8-digit code.\n\n" +
          "Get your code from: https://finmanager.netlify.app → Settings → Telegram Integration"
        );
        return;
      }

      // Check if code exists and is valid
      const { data: authCode, error: codeError } = await this.supabase
        .from('telegram_auth_codes')
        .select('*')
        .eq('code', code)
        .eq('is_used', false)
        .single();

      if (codeError || !authCode) {
        await this.bot.sendMessage(msg.chat.id,
          "❌ Invalid or expired code. Please generate a new code from the web app.\n\n" +
          "Get your code from: https://finmanager.netlify.app → Settings → Telegram Integration"
        );
        return;
      }

      // Check if user is already linked
      const { data: existingUser } = await this.supabase
        .from('telegram_users')
        .select('*')
        .eq('telegram_id', telegramUserId)
        .eq('is_active', true)
        .single();

      if (existingUser) {
        await this.bot.sendMessage(msg.chat.id,
          "✅ Your account is already linked!\n\n" +
          "You can start tracking your expenses right away:\n" +
          "• Say: \"Spent 500 on lunch\"\n" +
          "• Or use: `/expense 500 food lunch`"
        );
        return;
      }

      // Create telegram user record
      const { error: linkError } = await this.supabase
        .from('telegram_users')
        .insert({
          telegram_id: telegramUserId,
          user_id: authCode.user_id,
          username: msg.from.username || null,
          first_name: msg.from.first_name || null,
          last_name: msg.from.last_name || null,
          is_active: true,
          linked_at: new Date().toISOString()
        });

      if (linkError) throw linkError;

      // Mark code as used
      await this.supabase
        .from('telegram_auth_codes')
        .update({ is_used: true, used_at: new Date().toISOString() })
        .eq('code', code);

      await this.bot.sendMessage(msg.chat.id,
        "🎉 *Account Successfully Linked!*\n\n" +
        "Your Telegram account is now connected to FiNManageR.\n\n" +
        "*🚀 You can now:*\n" +
        "• Track expenses: \"Spent 500 on lunch\"\n" +
        "• Log income: \"Received 50000 salary\"\n" +
        "• Check balance: `/balance`\n" +
        "• View recent transactions: `/recent`\n" +
        "• See your categories: `/categories`\n\n" +
        "*💡 Try saying:* \"Spent 500 on food\" to log your first transaction!",
        { parse_mode: 'Markdown' }
      );

    } catch (error) {
      console.error('Link command error:', error);
      await this.bot.sendMessage(msg.chat.id,
        "❌ Failed to link account. Please try again.\n\n" +
        "If the problem persists, generate a new code from the web app."
      );
    }
  }

  async handleCategoriesCommand(msg) {
    try {
      const telegramUserId = msg.from?.id.toString();
      if (!telegramUserId) return;

      // Check authentication
      const telegramUser = await this.requireAuthentication(msg);
      if (!telegramUser) return;

      // Check cache first
      const cacheKey = `categories_${telegramUser.user_id}`;
      const cached = this.categoriesCache.get(cacheKey);
      
      if (cached && (Date.now() - cached.timestamp) < this.cacheExpiry) {
        await this.bot.sendMessage(msg.chat.id, cached.message, { parse_mode: 'Markdown' });
        return;
      }

      // Fetch user's categories from database
      const { data: categories, error } = await this.supabase
        .from('categories')
        .select('*')
        .eq('user_id', telegramUser.user_id)
        .order('name');

      if (error) throw error;

      // Separate categories by type
      const expenseCategories = categories.filter(cat => cat.type === 'expense');
      const incomeCategories = categories.filter(cat => cat.type === 'income');

      // Format categories
      const expenseCategoriesText = expenseCategories.length > 0 
        ? expenseCategories.map(cat => `• \`${cat.name.toLowerCase().replace(/[^a-z0-9]/g, '')}\` - ${cat.name}`).join('\n')
        : '• No expense categories found';

      const incomeCategoriesText = incomeCategories.length > 0
        ? incomeCategories.map(cat => `• \`${cat.name.toLowerCase().replace(/[^a-z0-9]/g, '')}\` - ${cat.name}`).join('\n')
        : '• No income categories found';

      const categoriesMessage = `
📂 *Your Personal Categories*

*💸 Expense Categories (${expenseCategories.length}):*
${expenseCategoriesText}

*💰 Income Categories (${incomeCategories.length}):*
${incomeCategoriesText}

*📝 Usage Examples:*
• \`/expense 500 ${expenseCategories[0]?.name.toLowerCase().replace(/[^a-z0-9]/g, '') || 'food'} Lunch at restaurant\`
• \`/income 50000 ${incomeCategories[0]?.name.toLowerCase().replace(/[^a-z0-9]/g, '') || 'salary'} Monthly salary\`

*🎯 Natural Language Examples:*
• "Spent 500 on ${expenseCategories[0]?.name || 'food'}"
• "Received 50000 from ${incomeCategories[0]?.name || 'salary'}"

*✨ Pro Tips:*
• Use category names exactly as shown above
• Categories are synced with your web app
• Add custom categories in the web app settings

💾 *Categories from your real FiNManageR account!*
      `;

      // Cache the result
      this.categoriesCache.set(cacheKey, {
        message: categoriesMessage,
        timestamp: Date.now()
      });

      await this.bot.sendMessage(msg.chat.id, categoriesMessage, { parse_mode: 'Markdown' });

    } catch (error) {
      console.error('Categories command error:', error);
      await this.bot.sendMessage(msg.chat.id,
        "❌ Failed to retrieve categories. Please try again.\n\n" +
        "If the problem persists, contact support through the web app."
      );
    }
  }

  async handleBalanceCommand(msg) {
    try {
      const telegramUserId = msg.from?.id.toString();
      if (!telegramUserId) return;

      const telegramUser = await this.requireAuthentication(msg);
      if (!telegramUser) return;

      // Get user's transactions to calculate balance
      const { data: transactions, error } = await this.supabase
        .from('transactions')
        .select('amount, type')
        .eq('user_id', telegramUser.user_id);

      if (error) throw error;

      let totalIncome = 0;
      let totalExpenses = 0;

      transactions.forEach(transaction => {
        if (transaction.type === 'income') {
          totalIncome += transaction.amount;
        } else if (transaction.type === 'expense') {
          totalExpenses += transaction.amount;
        }
      });

      const balance = totalIncome - totalExpenses;
      const balanceEmoji = balance >= 0 ? '💰' : '⚠️';

      const balanceMessage = `
${balanceEmoji} *Your Financial Summary*

*💰 Total Income:* ₹${totalIncome.toLocaleString()}
*💸 Total Expenses:* ₹${totalExpenses.toLocaleString()}
*📊 Current Balance:* ₹${balance.toLocaleString()}

*📈 Quick Stats:*
• Total Transactions: ${transactions.length}
• Average Transaction: ₹${transactions.length > 0 ? Math.round((totalIncome + totalExpenses) / transactions.length).toLocaleString() : '0'}

${balance < 0 ? '⚠️ *Note:* Your expenses exceed your income. Consider reviewing your spending patterns.' : '✅ *Great!* You\'re maintaining a positive balance.'}

Use \`/recent\` to see your latest transactions or \`/categories\` to view spending categories.
      `;

      await this.bot.sendMessage(msg.chat.id, balanceMessage, { parse_mode: 'Markdown' });

    } catch (error) {
      console.error('Balance command error:', error);
      await this.bot.sendMessage(msg.chat.id,
        "❌ Failed to retrieve balance. Please try again.\n\n" +
        "If the problem persists, contact support through the web app."
      );
    }
  }

  async handleRecentCommand(msg) {
    try {
      const telegramUserId = msg.from?.id.toString();
      if (!telegramUserId) return;

      const telegramUser = await this.requireAuthentication(msg);
      if (!telegramUser) return;

      // Get recent transactions
      const { data: transactions, error } = await this.supabase
        .from('transactions')
        .select('*')
        .eq('user_id', telegramUser.user_id)
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) throw error;

      if (!transactions || transactions.length === 0) {
        await this.bot.sendMessage(msg.chat.id,
          "📝 *No transactions found*\n\n" +
          "Start tracking your expenses:\n" +
          "• Say: \"Spent 500 on lunch\"\n" +
          "• Or use: `/expense 500 food lunch`"
        );
        return;
      }

      let recentMessage = `📋 *Your Recent Transactions*\n\n`;

      transactions.forEach((transaction, index) => {
        const emoji = transaction.type === 'income' ? '💰' : '💸';
        const date = new Date(transaction.created_at).toLocaleDateString();
        const amount = transaction.amount.toLocaleString();
        
        recentMessage += `${emoji} *₹${amount}* - ${transaction.category}\n`;
        recentMessage += `   ${transaction.description || 'No description'}\n`;
        recentMessage += `   📅 ${date}\n\n`;
      });

      recentMessage += `💡 *Tip:* Use \`/balance\` to see your overall financial summary.`;

      await this.bot.sendMessage(msg.chat.id, recentMessage, { parse_mode: 'Markdown' });

    } catch (error) {
      console.error('Recent command error:', error);
      await this.bot.sendMessage(msg.chat.id,
        "❌ Failed to retrieve recent transactions. Please try again.\n\n" +
        "If the problem persists, contact support through the web app."
      );
    }
  }

  async handleExpenseCommand(msg, params) {
    await this.handleTransactionCommand(msg, params, 'expense');
  }

  async handleIncomeCommand(msg, params) {
    await this.handleTransactionCommand(msg, params, 'income');
  }

  async handleTransactionCommand(msg, params, type) {
    try {
      const telegramUserId = msg.from?.id.toString();
      if (!telegramUserId) return;

      const telegramUser = await this.requireAuthentication(msg);
      if (!telegramUser) return;

      // Parse parameters: amount category description
      const parts = params.trim().split(' ');
      if (parts.length < 2) {
        await this.bot.sendMessage(msg.chat.id,
          `❌ Invalid format. Use: \`/${type} <amount> <category> <description>\`\n\n` +
          `Example: \`/${type} 500 food Lunch at restaurant\`\n\n` +
          `💡 Tip: Use \`/categories\` to see your personal ${type} categories`
        );
        return;
      }

      const amount = parseFloat(parts[0]);
      const category = parts[1];
      const description = parts.slice(2).join(' ') || `${type} transaction`;

      if (isNaN(amount) || amount <= 0) {
        await this.bot.sendMessage(msg.chat.id,
          "❌ Invalid amount. Please enter a valid positive number.\n\n" +
          `Example: \`/${type} 500 food Lunch\``
        );
        return;
      }

      // Create transaction
      const { data: transaction, error } = await this.supabase
        .from('transactions')
        .insert({
          user_id: telegramUser.user_id,
          amount: amount,
          type: type,
          category: category,
          description: description,
          date: new Date().toISOString(),
          metadata: {
            telegram_user_id: telegramUserId,
            telegram_username: msg.from.username || null,
            source_type: 'telegram_bot_command'
          }
        })
        .select()
        .single();

      if (error) throw error;

      const emoji = type === 'income' ? '💰' : '💸';
      const successMessage = `
${emoji} *Transaction Recorded Successfully!*

*Amount:* ₹${amount.toLocaleString()}
*Type:* ${type.charAt(0).toUpperCase() + type.slice(1)}
*Category:* ${category}
*Description:* ${description}
*Date:* ${new Date().toLocaleDateString()}

✅ Your transaction has been saved to your FiNManageR account.

Use \`/balance\` to see your updated balance or \`/recent\` to view recent transactions.
      `;

      await this.bot.sendMessage(msg.chat.id, successMessage, { parse_mode: 'Markdown' });

    } catch (error) {
      console.error('Transaction command error:', error);
      await this.bot.sendMessage(msg.chat.id,
        `❌ Failed to record ${type}. Please try again.\n\n` +
        "If the problem persists, contact support through the web app."
      );
    }
  }

  async handleStatusCommand(msg) {
    try {
      const telegramUserId = msg.from?.id.toString();
      if (!telegramUserId) return;

      const { data: telegramUser } = await this.supabase
        .from('telegram_users')
        .select('*')
        .eq('telegram_id', telegramUserId)
        .eq('is_active', true)
        .single();

      if (!telegramUser) {
        await this.bot.sendMessage(msg.chat.id,
          "🔗 *Account Not Linked*\n\n" +
          "Your Telegram account is not linked to FiNManageR.\n\n" +
          "To get started:\n" +
          "1. Visit: https://finmanager.netlify.app\n" +
          "2. Go to Settings → Telegram Integration\n" +
          "3. Generate an 8-digit code\n" +
          "4. Use: `/link <your-code>`"
        );
        return;
      }

      const statusMessage = `
✅ *Account Status: Linked*

*👤 Account Details:*
• Name: ${telegramUser.first_name || 'Not set'} ${telegramUser.last_name || ''}
• Username: @${telegramUser.username || 'Not set'}
• Linked: ${new Date(telegramUser.linked_at).toLocaleDateString()}

*🤖 Bot Features Available:*
• ✅ Natural Language Processing
• ✅ Transaction Logging
• ✅ Balance Tracking
• ✅ Category Management
• ✅ Recent Transactions

*🚀 Ready to use!* Start tracking your expenses by saying "Spent 500 on lunch" or use commands like \`/expense 500 food lunch\`.
      `;

      await this.bot.sendMessage(msg.chat.id, statusMessage, { parse_mode: 'Markdown' });

    } catch (error) {
      console.error('Status command error:', error);
      await this.bot.sendMessage(msg.chat.id,
        "❌ Failed to check status. Please try again."
      );
    }
  }

  // Natural Language Processing
  async handleNaturalLanguageMessage(msg) {
    try {
      const telegramUserId = msg.from?.id.toString();
      if (!telegramUserId) return;

      // Check authentication
      const telegramUser = await this.requireAuthentication(msg);
      if (!telegramUser) return;

      // Parse natural language
      const nlpResult = this.parseNaturalLanguage(msg.text);

      if (nlpResult.success && nlpResult.transaction) {
        const transaction = nlpResult.transaction;
        
        // Show confirmation with inline keyboard
        const confirmationMessage = `
🤖 *I understood your transaction:*

*Amount:* ₹${transaction.amount.toLocaleString()}
*Type:* ${transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)}
*Category:* ${transaction.category}
*Description:* ${transaction.description}
*Confidence:* ${nlpResult.confidence}%

Is this correct?
        `;

        const keyboard = {
          inline_keyboard: [
            [
              { text: '✅ Confirm', callback_data: `confirm_${Date.now()}` },
              { text: '❌ Cancel', callback_data: `cancel_${Date.now()}` }
            ]
          ]
        };

        // Store pending transaction
        const transactionId = Date.now().toString();
        this.pendingTransactions.set(transactionId, {
          userId: telegramUser.user_id,
          telegramUserId: telegramUserId,
          transaction: transaction,
          originalMessage: msg.text,
          timestamp: Date.now()
        });

        // Update keyboard with correct transaction ID
        keyboard.inline_keyboard[0][0].callback_data = `confirm_${transactionId}`;
        keyboard.inline_keyboard[0][1].callback_data = `cancel_${transactionId}`;

        await this.bot.sendMessage(msg.chat.id, confirmationMessage, {
          parse_mode: 'Markdown',
          reply_markup: keyboard
        });

      } else {
        // Provide helpful suggestions
        const suggestions = this.getNLPSuggestions(msg.text);
        let message = "🤔 I couldn't understand that transaction.\n\n" +
          "Try these formats:\n" +
          "• \"Spent 500 on lunch\"\n" +
          "• \"Paid 1200 for groceries\"\n" +
          "• \"Earned 50000 from salary\"\n\n";

        if (suggestions.length > 0) {
          message += "*💡 Suggestions:*\n" + suggestions.join('\n') + "\n\n";
        }

        message += "Or use commands like `/expense 500 food lunch` for direct entry.";

        await this.bot.sendMessage(msg.chat.id, message, { parse_mode: 'Markdown' });
      }

    } catch (error) {
      console.error('Natural language processing error:', error);
      await this.bot.sendMessage(msg.chat.id,
        "❌ I'm having trouble processing your message right now. Please try again or use specific commands.\n\n" +
        "💡 Try: `/expense 500 food Lunch` or `/income 50000 salary`"
      );
    }
  }

  parseNaturalLanguage(message) {
    const lowerMessage = message.toLowerCase();

    // Extract amount
    const amount = this.extractAmount(lowerMessage);
    if (!amount) {
      return { success: false, error: 'No amount found' };
    }

    // Determine transaction type
    const type = this.determineTransactionType(lowerMessage);
    if (!type) {
      return { success: false, error: 'Cannot determine transaction type' };
    }

    // Extract category
    const category = this.extractCategory(lowerMessage, type);
    if (!category) {
      return { success: false, error: 'Cannot determine category' };
    }

    // Extract description
    const description = this.extractDescription(message, amount, category);

    // Calculate confidence
    const confidence = this.calculateConfidence(lowerMessage, amount, type, category);

    return {
      success: true,
      confidence: confidence,
      transaction: {
        amount: amount,
        type: type,
        category: category,
        description: description
      }
    };
  }

  extractAmount(message) {
    // Look for various amount patterns
    const patterns = [
      /(?:₹|rs\.?|rupees?)\s*(\d+(?:,\d{3})*(?:\.\d{2})?)/i,
      /(\d+(?:,\d{3})*(?:\.\d{2})?)\s*(?:₹|rs\.?|rupees?)/i,
      /(\d+(?:,\d{3})*(?:\.\d{2})?)/
    ];

    for (const pattern of patterns) {
      const match = message.match(pattern);
      if (match) {
        const amount = parseFloat(match[1].replace(/,/g, ''));
        if (amount > 0 && amount < 10000000) { // Reasonable limits
          return amount;
        }
      }
    }

    return null;
  }

  determineTransactionType(message) {
    const expenseKeywords = ['spent', 'paid', 'bought', 'purchase', 'expense', 'cost', 'bill'];
    const incomeKeywords = ['earned', 'received', 'income', 'salary', 'got', 'profit'];

    const hasExpenseKeyword = expenseKeywords.some(keyword => message.includes(keyword));
    const hasIncomeKeyword = incomeKeywords.some(keyword => message.includes(keyword));

    if (hasExpenseKeyword && !hasIncomeKeyword) return 'expense';
    if (hasIncomeKeyword && !hasExpenseKeyword) return 'income';

    // Default to expense if ambiguous
    return 'expense';
  }

  extractCategory(message, type) {
    const expenseCategories = [
      'food', 'groceries', 'transport', 'transportation', 'shopping', 'entertainment',
      'bills', 'utilities', 'healthcare', 'education', 'travel', 'rent', 'fuel',
      'coffee', 'lunch', 'dinner', 'breakfast', 'snacks', 'restaurant'
    ];

    const incomeCategories = [
      'salary', 'freelance', 'business', 'investment', 'rental', 'interest',
      'dividend', 'gift', 'refund', 'bonus', 'commission'
    ];

    const categories = type === 'expense' ? expenseCategories : incomeCategories;

    for (const category of categories) {
      if (message.includes(category)) {
        return category;
      }
    }

    return type === 'expense' ? 'other' : 'other';
  }

  extractDescription(message, amount, category) {
    // Remove amount and common words to get description
    let description = message
      .replace(/₹|rs\.?|rupees?/gi, '')
      .replace(new RegExp(amount.toString(), 'g'), '')
      .replace(/spent|paid|bought|earned|received/gi, '')
      .replace(/on|for|from/gi, '')
      .replace(new RegExp(category, 'gi'), '')
      .trim();

    return description || `${category} transaction`;
  }

  calculateConfidence(message, amount, type, category) {
    let confidence = 50; // Base confidence

    // Amount found
    if (amount) confidence += 20;

    // Type keywords found
    const expenseKeywords = ['spent', 'paid', 'bought'];
    const incomeKeywords = ['earned', 'received'];
    const keywords = type === 'expense' ? expenseKeywords : incomeKeywords;
    
    if (keywords.some(keyword => message.includes(keyword))) {
      confidence += 20;
    }

    // Category found
    if (category && category !== 'other') confidence += 15;

    // Message structure
    if (message.includes('on') || message.includes('for') || message.includes('from')) {
      confidence += 10;
    }

    return Math.min(confidence, 95); // Cap at 95%
  }

  getNLPSuggestions(message) {
    const suggestions = [];

    if (!this.extractAmount(message)) {
      suggestions.push("💡 Include the amount: 'Spent ₹500 on lunch'");
    }

    if (!message.match(/spent|paid|bought|earned|received/i)) {
      suggestions.push("💡 Use action words: 'Spent', 'Paid', 'Earned', 'Received'");
    }

    if (!message.match(/on|for|from/i)) {
      suggestions.push("💡 Use connecting words: 'on', 'for', 'from'");
    }

    return suggestions;
  }

  async handleCallbackQuery(query) {
    try {
      const [action, transactionId] = query.data.split('_');
      const pendingTransaction = this.pendingTransactions.get(transactionId);

      if (!pendingTransaction) {
        await this.bot.answerCallbackQuery(query.id, {
          text: "Transaction expired. Please try again.",
          show_alert: true
        });
        return;
      }

      if (action === 'confirm') {
        // Create the transaction
        const { error } = await this.supabase
          .from('transactions')
          .insert({
            user_id: pendingTransaction.userId,
            amount: pendingTransaction.transaction.amount,
            type: pendingTransaction.transaction.type,
            category: pendingTransaction.transaction.category,
            description: pendingTransaction.transaction.description,
            date: new Date().toISOString(),
            metadata: {
              telegram_user_id: pendingTransaction.telegramUserId,
              source_type: 'telegram_bot_nlp',
              original_message: pendingTransaction.originalMessage
            }
          });

        if (error) throw error;

        const emoji = pendingTransaction.transaction.type === 'income' ? '💰' : '💸';
        await this.bot.editMessageText(
          `${emoji} *Transaction Confirmed!*\n\n` +
          `₹${pendingTransaction.transaction.amount.toLocaleString()} ${pendingTransaction.transaction.type} recorded successfully.\n\n` +
          `Use \`/balance\` to see your updated balance.`,
          {
            chat_id: query.message.chat.id,
            message_id: query.message.message_id,
            parse_mode: 'Markdown'
          }
        );

        await this.bot.answerCallbackQuery(query.id, {
          text: "Transaction recorded successfully!",
          show_alert: false
        });

      } else if (action === 'cancel') {
        await this.bot.editMessageText(
          "❌ Transaction cancelled.\n\nTry again with a different format or use commands like `/expense 500 food lunch`.",
          {
            chat_id: query.message.chat.id,
            message_id: query.message.message_id
          }
        );

        await this.bot.answerCallbackQuery(query.id, {
          text: "Transaction cancelled",
          show_alert: false
        });
      }

      // Clean up pending transaction
      this.pendingTransactions.delete(transactionId);

    } catch (error) {
      console.error('Callback query error:', error);
      await this.bot.answerCallbackQuery(query.id, {
        text: "Error processing transaction. Please try again.",
        show_alert: true
      });
    }
  }

  async requireAuthentication(msg) {
    const telegramUserId = msg.from?.id.toString();
    if (!telegramUserId) return null;

    const { data: telegramUser } = await this.supabase
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', telegramUserId)
      .eq('is_active', true)
      .single();

    if (!telegramUser) {
      await this.bot.sendMessage(msg.chat.id,
        "🔗 *Please link your account first*\n\n" +
        "To use this feature, you need to link your Telegram account to FiNManageR.\n\n" +
        "*Steps:*\n" +
        "1. Visit: https://finmanager.netlify.app\n" +
        "2. Go to Settings → Telegram Integration\n" +
        "3. Generate an 8-digit code\n" +
        "4. Use: `/link <your-code>`\n\n" +
        "Once linked, you can use all bot features!",
        { parse_mode: 'Markdown' }
      );
      return null;
    }

    return telegramUser;
  }
}

// Start the optimized bot only if this file is run directly
if (require.main === module) {
  const bot = new OptimizedTelegramBot(process.env.TELEGRAM_BOT_TOKEN);
  console.log('🚀 Optimized Telegram Bot is running...');
}

module.exports = OptimizedTelegramBot;
