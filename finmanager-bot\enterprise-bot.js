require('dotenv').config();
const TelegramBot = require('node-telegram-bot-api');
const { createClient } = require('@supabase/supabase-js');
const TelegramGCSService = require('../scripts/telegram-gcs-service.cjs');
const RealVoiceService = require('../scripts/real-voice-service.cjs');
const PushNotificationService = require('../scripts/push-notification-service.cjs');
const MonitoringService = require('../scripts/monitoring-service.cjs');
const I18nService = require('../scripts/i18n-service.cjs');
const SecurityService = require('../scripts/security-service.cjs');
const EnhancedConfirmationService = require('../scripts/enhanced-confirmation-service.cjs');
const AdvancedObservabilityService = require('../scripts/advanced-observability-service.cjs');
const CollaborationService = require('../scripts/collaboration-service.cjs');
// ConversationContextService REMOVED - Enhanced Conversational AI eliminated
const PredictiveAlertSystem = require('../scripts/predictive-alert-system.cjs');
const AdvancedVoiceIntelligence = require('../scripts/advanced-voice-intelligence.cjs');
const SmartSchedulingAI = require('../scripts/smart-scheduling-ai.cjs');

// Production Telegram Bot with Real Database Integration
class ProductionTelegramBot {
  constructor(token) {
    this.bot = new TelegramBot(token, { polling: true });
    this.supabase = this.initializeSupabase();

    // Initialize Phase 6 enterprise services
    this.monitoringService = new MonitoringService();
    this.i18nService = new I18nService();
    this.securityService = new SecurityService(this.supabase);
    this.attachmentService = new TelegramGCSService(this.supabase);

    // Pending attachments for transactions
    this.pendingAttachments = new Map();
    this.voiceService = new RealVoiceService();
    this.pushNotificationService = new PushNotificationService(this.bot, this.supabase);

    // Initialize Phase 7.3 & 7.4 advanced services
    console.log('🔧 Initializing Enhanced Confirmation Service...');
    this.enhancedConfirmationService = new EnhancedConfirmationService(this.bot, this.supabase, this.i18nService);
    console.log('🔧 Initializing Advanced Observability Service...');
    this.advancedObservabilityService = new AdvancedObservabilityService();
    console.log('🔧 Initializing Collaboration Service...');
    this.collaborationService = new CollaborationService(this.supabase, this.bot, this.i18nService);
    // Enhanced Conversational AI initialization REMOVED - Resource waste eliminated
    console.log('🔧 Initializing Predictive Alert System...');
    this.predictiveAlertSystem = new PredictiveAlertSystem(this.supabase, this.bot, this.i18nService);
    console.log('🔧 Initializing Advanced Voice Intelligence...');
    this.advancedVoiceIntelligence = new AdvancedVoiceIntelligence(this.supabase, this.bot, this.i18nService);
    console.log('🔧 Initializing Smart Scheduling AI...');
    this.smartSchedulingAI = new SmartSchedulingAI(this.supabase, this.bot, this.i18nService);

    console.log('🔧 Setting up commands...');
    this.setupCommands();
    console.log('🔧 Setting up event handlers...');
    this.setupEventHandlers();

    // Initialize GCS service
    console.log('🔧 Initializing GCS service...');
    this.initializeAttachmentService();

    // Cache for pending transactions (now handled by enhanced confirmation)
    this.pendingTransactions = new Map();

    console.log('🚀 Production TelegramBot initialized with Phase 7 Advanced Features');
    console.log('✅ All services initialized successfully');
    console.log('✅ Enhanced UX, Advanced Observability, and Collaboration active');
    console.log('🔮 Predictive Alert System monitoring for proactive insights');
    console.log('🎤 Advanced Voice Intelligence ready for natural voice commands');
    console.log('📅 Smart Scheduling AI optimizing user reminders and schedules');
    console.log('🏆 World-class AI assistant ready for global deployment!');
  }

  /**
   * Initialize GCS service
   */
  async initializeAttachmentService() {
    try {
      const initialized = await this.attachmentService.initialize();
      if (initialized) {
        console.log('✅ GCS service initialized for Telegram bot');
      } else {
        console.log('⚠️ GCS not configured - attachment features disabled');
      }
    } catch (error) {
      console.error('❌ Failed to initialize attachment service:', error);
    }
  }

  initializeSupabase() {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseKey) {
      console.error('❌ Missing Supabase credentials');
      throw new Error('Supabase credentials not found');
    }

    console.log('✅ Supabase client initialized');
    return createClient(supabaseUrl, supabaseKey, {
      auth: {
        persistSession: false,
        autoRefreshToken: false,
        storageKey: 'finmanager-production-bot-auth'
      },
      global: {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'x-application-name': 'financial-management-production-bot',
          'X-Client-Info': 'finmanager-production-bot'
        }
      }
    });
  }

  setupCommands() {
    // Welcome command
    this.bot.onText(/\/start/, (msg) => {
      const welcomeMessage = `
🎉 *Welcome to FiNManageR!*

Your intelligent personal finance assistant is here to help you take control of your money! 💰

*✨ What I can do for you:*
• 📊 Track your expenses and income automatically
• 🤖 Understand natural language - just tell me what you spent!
• 📸 Scan receipts with smart OCR technology
• 🎤 Process voice messages for hands-free logging
• 📈 Provide personalized financial insights
• 🔔 Send smart alerts to keep you on track
• 🔒 Keep your data secure with enterprise-grade protection

*🚀 Getting Started:*
1️⃣ Link your FiNManageR account: \`/link <auth_code>\`
2️⃣ Start tracking: \`/expense 500 food Lunch at restaurant\`
3️⃣ Check your progress: \`/balance\` and \`/insights\`

*💡 Quick Examples:*
• "Spent 1200 on groceries at Walmart"
• \`/income 75000 salary Monthly paycheck\`
• \`/balance\` - See your current financial status
• \`/recent\` - View your latest transactions

*🌟 Smart Features:*
• 💾 **Secure Storage**: All your data safely stored in your account
• 🤖 **Smart Recognition**: AI understands your spending patterns
• 💬 **Natural Language**: Just say "Spent 500 on lunch" - I'll understand!
• 🎤 **Voice Commands**: Talk to me naturally about your expenses
• 🔔 **Smart Alerts**: Get notified about important financial events
• 📊 **Insights**: Discover trends and optimize your spending
• 🔒 **Privacy First**: Your financial data is always protected

*🔗 Need to connect your account?*
Visit FiNManageR web app → Settings → Telegram Integration → Generate Auth Code

Ready to transform your financial life? Let's get started! 🚀
      `;
      this.bot.sendMessage(msg.chat.id, welcomeMessage, { parse_mode: 'Markdown' });
    });

    // Help command
    this.bot.onText(/\/help/, (msg) => {
      const helpMessage = `
📋 *FiNManageR Commands & Features:*

*🔗 Account Setup:*
• \`/start\` - Welcome & getting started guide
• \`/link <auth_code>\` - Connect your FiNManageR account
• \`/status\` - Check your account connection
• \`/unlink\` - Disconnect account

*💰 Track Your Money:*
• \`/expense <amount> <category> [description]\`
  Example: \`/expense 500 food Lunch at McDonald's\`
• \`/income <amount> <source> [description]\`
  Example: \`/income 50000 salary Monthly paycheck\`

*📊 View Your Finances:*
• \`/balance\` - See your current financial status
• \`/recent\` - View your latest transactions
• \`/categories\` - View your personal expense & income categories
• \`/insights\` - Get personalized financial insights

*🌟 Smart Features:*
• 💬 **Natural Language:** Just say "Spent 500 on lunch" - I'll understand!
• ⚡ **Quick Commands:** Fast expense logging with simple commands
• 🎤 **Voice Commands:** Record voice messages for hands-free tracking
• 🧠 **Smart Analytics:** Discover your spending patterns and trends

*✨ What Makes Me Special:*
• 🔒 **Secure & Private:** Your data is always protected
• 🤖 **AI-Powered:** Smart recognition and personalized insights
• ⚡ **Real-Time:** Instant updates across all your devices
• 🎯 **Accurate:** Smart confirmation system prevents errors
• 📈 **Insightful:** Learn about your financial habits

*💡 Pro Tips:*
• Use natural language - I understand how you naturally speak about money
• Try quick commands like \`/expense 500 food lunch\` for fastest entry
• Check your insights regularly to discover spending patterns
• Set up alerts to stay on top of your finances

Need more help? Visit the FiNManageR web app for detailed guides! 🌐
      `;
      this.bot.sendMessage(msg.chat.id, helpMessage, { parse_mode: 'Markdown' });
    });

    // Link command with real authentication
    this.bot.onText(/\/link (.+)/, async (msg, match) => {
      await this.handleLinkAccount(msg, match[1]);
    });

    // Status command with real data
    this.bot.onText(/\/status/, async (msg) => {
      await this.handleStatus(msg);
    });

    // Categories command - fetch real categories from database
    this.bot.onText(/\/categories/, async (msg) => {
      await this.handleCategoriesCommand(msg);
    });

    // Real expense command with database integration
    this.bot.onText(/\/expense\s+(\d+(?:\.\d{1,2})?)\s+(\w+)(?:\s+(.+))?/, async (msg, match) => {
      await this.handleExpenseCommand(msg, match);
    });

    // Real income command with database integration
    this.bot.onText(/\/income\s+(\d+(?:\.\d{1,2})?)\s+(\w+)(?:\s+(.+))?/, async (msg, match) => {
      await this.handleIncomeCommand(msg, match);
    });

    // Real balance command with database query
    this.bot.onText(/\/balance/, async (msg) => {
      await this.handleBalanceCommand(msg);
    });

    // Real recent transactions command
    this.bot.onText(/\/recent/, async (msg) => {
      await this.handleRecentCommand(msg);
    });

    // Insights command
    this.bot.onText(/\/insights/, async (msg) => {
      await this.handleInsightsCommand(msg);
    });

    // Predictive alerts command (for testing)
    this.bot.onText(/\/predict/, async (msg) => {
      await this.handlePredictiveAnalysisCommand(msg);
    });

    // Voice commands help
    this.bot.onText(/\/voice/, async (msg) => {
      await this.handleVoiceCommandsHelp(msg);
    });

    // Smart scheduling command
    this.bot.onText(/\/schedule/, async (msg) => {
      await this.handleSmartSchedulingCommand(msg);
    });

    // Confirmation handlers
    this.bot.onText(/^(yes|confirm|✅)$/i, async (msg) => {
      await this.handleConfirmTransaction(msg, true);
    });

    this.bot.onText(/^(no|cancel|❌)$/i, async (msg) => {
      await this.handleConfirmTransaction(msg, false);
    });

    // Fallback for invalid commands
    this.bot.onText(/\/expense(?:\s|$)/, (msg) => {
      if (!msg.text.match(/\/expense\s+(\d+(?:\.\d{1,2})?)\s+(\w+)(?:\s+(.+))?/)) {
        this.bot.sendMessage(msg.chat.id,
          `💡 *Expense Logging - Production*\n\n` +
          `Usage: \`/expense <amount> <category> [description]\`\n\n` +
          `Examples:\n` +
          `• \`/expense 500 food Lunch at restaurant\`\n` +
          `• \`/expense 1200 transport Uber ride\`\n` +
          `• \`/expense 2500 entertainment Movie tickets\`\n\n` +
          `💡 Tip: Use \`/categories\` to see your personal expense categories\n` +
          `🔥 All transactions are saved to your real account!`,
          { parse_mode: 'Markdown' }
        );
      }
    });

    this.bot.onText(/\/income(?:\s|$)/, (msg) => {
      if (!msg.text.match(/\/income\s+(\d+(?:\.\d{1,2})?)\s+(\w+)(?:\s+(.+))?/)) {
        this.bot.sendMessage(msg.chat.id,
          `💡 *Income Logging - Production*\n\n` +
          `Usage: \`/income <amount> <source> [description]\`\n\n` +
          `Examples:\n` +
          `• \`/income 50000 salary Monthly salary\`\n` +
          `• \`/income 5000 freelance Project payment\`\n` +
          `• \`/income 1000 investment Dividend\`\n\n` +
          `💡 Tip: Use \`/categories\` to see your personal income categories\n` +
          `🔥 All income is saved to your real account!`,
          { parse_mode: 'Markdown' }
        );
      }
    });
  }

  setupEventHandlers() {
    // Handle non-command messages with Phase 4 NLP and attachments
    this.bot.on('message', async (msg) => {
      if (msg.text && !msg.text.startsWith('/')) {
        await this.handleNaturalLanguageMessage(msg);
      } else if (msg.photo) {
        await this.handlePhotoMessage(msg);
      } else if (msg.document) {
        await this.handleDocumentMessage(msg);
      } else if (msg.voice) {
        await this.handleAdvancedVoiceMessage(msg);
      }
    });

    // Handle callback queries for attachments
    this.bot.on('callback_query', async (callbackQuery) => {
      const { data } = callbackQuery;

      if (data.startsWith('attach_') || data.startsWith('cancel_attachment_')) {
        await this.handleAttachmentCallback(callbackQuery);
      }
    });

    // Error handling
    this.bot.on('error', (error) => {
      console.error('Telegram Bot Error:', error);
    });

    this.bot.on('polling_error', (error) => {
      console.error('Telegram Bot Polling Error:', error);
    });
  }

  // Real Database Integration Methods
  async handleLinkAccount(msg, authCode) {
    try {
      const telegramUserId = msg.from?.id.toString();
      if (!telegramUserId) return;

      // Check if already linked
      const { data: existingLink } = await this.supabase
        .from('telegram_users')
        .select('*')
        .eq('telegram_id', telegramUserId)
        .single();

      if (existingLink) {
        await this.bot.sendMessage(msg.chat.id,
          "✅ Your account is already linked!\n\n" +
          "Use `/unlink` if you want to link a different account."
        );
        return;
      }

      // Verify auth code
      const { data: authData } = await this.supabase
        .from('telegram_auth_codes')
        .select('user_id')
        .eq('code', authCode)
        .eq('is_used', false)
        .gte('expires_at', new Date().toISOString())
        .single();

      if (!authData) {
        await this.bot.sendMessage(msg.chat.id,
          "❌ Invalid or expired auth code.\n\n" +
          "Please generate a new auth code from the web app and try again."
        );
        return;
      }

      // Link account
      const { error: linkError } = await this.supabase
        .from('telegram_users')
        .insert({
          telegram_id: telegramUserId,
          user_id: authData.user_id,
          username: msg.from?.username,
          first_name: msg.from?.first_name,
          last_name: msg.from?.last_name,
          language_code: msg.from?.language_code,
          is_active: true,
          linked_at: new Date().toISOString()
        });

      if (linkError) throw linkError;

      // Mark auth code as used
      await this.supabase
        .from('telegram_auth_codes')
        .update({ is_used: true })
        .eq('code', authCode);

      await this.bot.sendMessage(msg.chat.id,
        "🎉 *Welcome to FiNManageR!*\n\n" +
        "Your account is now connected and ready to use! Here's what you can do:\n\n" +
        "💰 **Track Your Money:**\n" +
        "• Log expenses: `/expense 500 food Lunch`\n" +
        "• Log income: `/income 75000 salary Paycheck`\n" +
        "• Or just tell me: \"Spent 1200 on groceries\"\n\n" +
        "📊 **Stay Informed:**\n" +
        "• Check balance: `/balance`\n" +
        "• View transactions: `/recent`\n" +
        "• Get insights: `/insights`\n\n" +
        "Ready to take control of your finances? Let's start! 🚀",
        { parse_mode: 'Markdown' }
      );

      // Log interaction
      await this.logInteraction(telegramUserId, '/link', 'Account linked successfully', true);

    } catch (error) {
      console.error('Link account error:', error);
      await this.bot.sendMessage(msg.chat.id,
        "❌ Failed to link account. Please try again or contact support."
      );
    }
  }

  async handleStatus(msg) {
    try {
      const telegramUserId = msg.from?.id.toString();
      if (!telegramUserId) return;

      const { data: telegramUser } = await this.supabase
        .from('telegram_users')
        .select('*')
        .eq('telegram_id', telegramUserId)
        .single();

      if (!telegramUser) {
        await this.bot.sendMessage(msg.chat.id,
          "❌ *Account Not Linked*\n\n" +
          "Use `/link <auth_code>` to connect your FiNManageR account.\n\n" +
          "Get auth code from: Settings → Telegram Integration",
          { parse_mode: 'Markdown' }
        );
        return;
      }

      // Get user's transaction stats
      const { data: stats } = await this.supabase
        .from('transactions')
        .select('type, amount')
        .eq('user_id', telegramUser.user_id);

      const totalTransactions = stats?.length || 0;
      const totalIncome = stats?.filter(t => t.type === 'income').reduce((sum, t) => sum + t.amount, 0) || 0;
      const totalExpenses = stats?.filter(t => t.type === 'expense').reduce((sum, t) => sum + t.amount, 0) || 0;

      const statusMessage = `
✅ *Account Status: Linked & Active*

*Account Details:*
• Telegram ID: \`${telegramUser.telegram_id}\`
• Username: ${telegramUser.username || 'Not set'}
• Name: ${telegramUser.first_name || ''} ${telegramUser.last_name || ''}
• Linked: ${new Date(telegramUser.linked_at).toLocaleDateString()}
• Status: ${telegramUser.is_active ? '🟢 Active' : '🔴 Inactive'}

*Transaction Summary:*
• Total Transactions: ${totalTransactions}
• Total Income: ₹${totalIncome.toLocaleString()}
• Total Expenses: ₹${totalExpenses.toLocaleString()}
• Net Balance: ₹${(totalIncome - totalExpenses).toLocaleString()}

*Available Commands:*
• \`/expense\` - Log real expenses
• \`/income\` - Log real income
• \`/balance\` - Check actual balance
• \`/recent\` - Real recent transactions
• \`/insights\` - AI spending analysis

Ready to track your real finances! 💰
      `;

      await this.bot.sendMessage(msg.chat.id, statusMessage, { parse_mode: 'Markdown' });
      await this.logInteraction(telegramUserId, '/status', 'Status checked', true);

    } catch (error) {
      console.error('Status check error:', error);
      await this.bot.sendMessage(msg.chat.id,
        "❌ Failed to check status. Please try again."
      );
    }
  }

  async handleExpenseCommand(msg, match) {
    try {
      const telegramUserId = msg.from?.id.toString();
      if (!telegramUserId) return;

      // Check authentication
      const telegramUser = await this.requireAuthentication(msg);
      if (!telegramUser) return;

      const amount = parseFloat(match[1]);
      const category = match[2].toLowerCase().trim();
      const description = match[3]?.trim() || '';

      // Create enhanced transaction data for confirmation
      const transactionData = {
        userId: telegramUser.user_id,
        amount: amount,
        type: 'expense',
        category: category,
        description: description || `${category} expense`,
        date: new Date().toISOString(),
        confidence: 0.95, // High confidence for direct commands
        originalMessage: msg.text,
        payment_method: 'Cash', // Default payment method
        location: null,
        metadata: {
          telegram_user_id: telegramUserId,
          telegram_username: msg.from.username || null,
          source_type: 'telegram_bot_command'
        }
      };

      // Use enhanced confirmation service to show the advanced transaction form
      await this.enhancedConfirmationService.sendTransactionConfirmation(
        msg.chat.id,
        transactionData,
        'en', // Default language
        msg.message_id
      );

      console.log(`Enhanced expense form shown: ₹${amount} ${category} for user ${telegramUser.user_id}`);

    } catch (error) {
      console.error('Enhanced expense error:', error);
      await this.bot.sendMessage(msg.chat.id, "❌ An error occurred while processing the expense. Please try again.");
    }
  }

  async handleIncomeCommand(msg, match) {
    try {
      const telegramUserId = msg.from?.id.toString();
      if (!telegramUserId) return;

      // Check authentication
      const telegramUser = await this.requireAuthentication(msg);
      if (!telegramUser) return;

      const amount = parseFloat(match[1]);
      const source = match[2].toLowerCase().trim();
      const description = match[3]?.trim() || '';

      // Create enhanced transaction data for confirmation
      const transactionData = {
        userId: telegramUser.user_id,
        amount: amount,
        type: 'income',
        category: source, // For income, source becomes category
        description: description || `${source} income`,
        date: new Date().toISOString(),
        confidence: 0.95, // High confidence for direct commands
        originalMessage: msg.text,
        payment_method: 'Bank Transfer', // Default payment method for income
        location: null,
        metadata: {
          telegram_user_id: telegramUserId,
          telegram_username: msg.from.username || null,
          source_type: 'telegram_bot_command',
          income_source: source // Store original source in metadata
        }
      };

      // Use enhanced confirmation service to show the advanced transaction form
      await this.enhancedConfirmationService.sendTransactionConfirmation(
        msg.chat.id,
        transactionData,
        'en', // Default language
        msg.message_id
      );

      console.log(`Enhanced income form shown: ₹${amount} ${source} for user ${telegramUser.user_id}`);

    } catch (error) {
      console.error('Enhanced income error:', error);
      await this.bot.sendMessage(msg.chat.id, "❌ An error occurred while processing the income. Please try again.");
    }
  }

  async handleBalanceCommand(msg) {
    try {
      const telegramUserId = msg.from?.id.toString();
      if (!telegramUserId) return;

      // Check authentication
      const telegramUser = await this.requireAuthentication(msg);
      if (!telegramUser) return;

      // Get current month transactions
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

      const { data: transactions, error } = await this.supabase
        .from('transactions')
        .select('*')
        .eq('user_id', telegramUser.user_id)
        .gte('date', startOfMonth.toISOString())
        .lte('date', endOfMonth.toISOString())
        .order('date', { ascending: false });

      if (error) throw error;

      // Calculate balances
      const totalIncome = transactions
        .filter(t => t.type === 'income')
        .reduce((sum, t) => sum + t.amount, 0);

      const totalExpenses = transactions
        .filter(t => t.type === 'expense')
        .reduce((sum, t) => sum + t.amount, 0);

      const currentBalance = totalIncome - totalExpenses;

      // Get category breakdown
      const expensesByCategory = transactions
        .filter(t => t.type === 'expense')
        .reduce((acc, t) => {
          acc[t.category] = (acc[t.category] || 0) + t.amount;
          return acc;
        }, {});

      const topCategories = Object.entries(expensesByCategory)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 3);

      const balanceMessage = `
📊 *Your Real Financial Summary*

💰 **Current Month Balance:** ${currentBalance >= 0 ? '₹' : '-₹'}${Math.abs(currentBalance).toLocaleString()}
${currentBalance >= 0 ? '✅ Positive balance' : '⚠️ Negative balance'}

📈 **This Month (${now.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })})**
• **Income:** ₹${totalIncome.toLocaleString()}
• **Expenses:** ₹${totalExpenses.toLocaleString()}
• **Transactions:** ${transactions.length}

${topCategories.length > 0 ? `🏷️ **Top Expense Categories:**
${topCategories.map(([cat, amount]) =>
  `• ${cat.charAt(0).toUpperCase() + cat.slice(1)}: ₹${amount.toLocaleString()}`
).join('\n')}` : ''}

📱 **Quick Actions:**
• \`/expense 500 food Lunch\` - Log expense
• \`/income 50000 salary\` - Log income
• \`/recent\` - Recent transactions
• \`/insights\` - AI spending analysis

💾 *Data from your real FiNManageR account!*
      `;

      await this.bot.sendMessage(msg.chat.id, balanceMessage, { parse_mode: 'Markdown' });
      await this.logInteraction(telegramUserId, '/balance', `Balance checked: ₹${currentBalance.toLocaleString()}`, true);

    } catch (error) {
      console.error('Balance check error:', error);
      await this.bot.sendMessage(msg.chat.id,
        "❌ Failed to retrieve balance. Please try again.\n\n" +
        "If the problem persists, contact support through the web app."
      );
    }
  }

  async handleRecentCommand(msg) {
    try {
      const telegramUserId = msg.from?.id.toString();
      if (!telegramUserId) return;

      // Check authentication
      const telegramUser = await this.requireAuthentication(msg);
      if (!telegramUser) return;

      // Get recent transactions
      const { data: transactions, error } = await this.supabase
        .from('transactions')
        .select('*')
        .eq('user_id', telegramUser.user_id)
        .order('date', { ascending: false })
        .limit(5);

      if (error) throw error;

      if (!transactions || transactions.length === 0) {
        await this.bot.sendMessage(msg.chat.id,
          "🌟 *Ready to Start Your Financial Journey?*\n\n" +
          "You haven't recorded any transactions yet - let's change that!\n\n" +
          "**Easy Ways to Get Started:**\n" +
          "💰 Log an expense: `/expense 500 food Lunch at cafe`\n" +
          "💵 Record income: `/income 75000 salary Monthly paycheck`\n" +
          "💬 Or just tell me: \"Spent 1200 on groceries today\"\n\n" +
          "Every financial journey starts with a single step. Ready to take yours? 🚀",
          { parse_mode: 'Markdown' }
        );
        return;
      }

      // Format transactions
      const transactionsList = transactions.map((transaction, index) => {
        const date = new Date(transaction.date);
        const emoji = transaction.type === 'income' ? '💰' : '💸';
        const sign = transaction.type === 'income' ? '+' : '-';

        return `${index + 1}. ${emoji} **${sign}₹${transaction.amount.toLocaleString()}**
   📂 ${transaction.category.charAt(0).toUpperCase() + transaction.category.slice(1)}
   📅 ${date.toLocaleDateString()} ${date.toLocaleTimeString()}
   ${transaction.description ? `📝 ${transaction.description}` : ''}
   🆔 ID: ${transaction.id}
   ${transaction.source_type === 'telegram_bot' ? '📱 *Via Telegram*' : '🌐 *Via Web App*'}`;
      }).join('\n\n');

      const recentMessage = `
📋 *Recent Transactions (Real Data)*

${transactionsList}

📱 **Quick Actions:**
• \`/expense 500 food Lunch\` - Log expense
• \`/income 50000 salary\` - Log income
• \`/balance\` - Check balance
• \`/insights\` - AI analysis

💾 *Data from your real FiNManageR account!*
      `;

      await this.bot.sendMessage(msg.chat.id, recentMessage, { parse_mode: 'Markdown' });
      await this.logInteraction(telegramUserId, '/recent', `Viewed recent transactions: ${transactions.length} found`, true);

    } catch (error) {
      console.error('Recent transactions error:', error);
      await this.bot.sendMessage(msg.chat.id,
        "❌ Failed to retrieve recent transactions. Please try again.\n\n" +
        "If the problem persists, contact support through the web app."
      );
    }
  }

  async handleCategoriesCommand(msg) {
    try {
      const telegramUserId = msg.from?.id.toString();
      if (!telegramUserId) return;

      // Check authentication
      const telegramUser = await this.requireAuthentication(msg);
      if (!telegramUser) return;

      // Fetch user's categories from database
      const { data: categories, error } = await this.supabase
        .from('categories')
        .select('*')
        .eq('user_id', telegramUser.user_id)
        .order('name');

      if (error) throw error;

      // Separate categories by type
      const expenseCategories = categories.filter(cat => cat.type === 'expense');
      const incomeCategories = categories.filter(cat => cat.type === 'income');

      // Format expense categories
      let expenseCategoriesText = '';
      if (expenseCategories.length > 0) {
        expenseCategoriesText = expenseCategories
          .map(cat => {
            const icon = cat.icon || '💰';
            const isDefault = cat.is_default ? '' : ' (Custom)';
            return `• \`${cat.name.toLowerCase().replace(/[^a-z0-9]/g, '')}\` - ${cat.name}${isDefault}`;
          })
          .join('\n');
      } else {
        expenseCategoriesText = '• No expense categories found';
      }

      // Format income categories
      let incomeCategoriesText = '';
      if (incomeCategories.length > 0) {
        incomeCategoriesText = incomeCategories
          .map(cat => {
            const icon = cat.icon || '💵';
            const isDefault = cat.is_default ? '' : ' (Custom)';
            return `• \`${cat.name.toLowerCase().replace(/[^a-z0-9]/g, '')}\` - ${cat.name}${isDefault}`;
          })
          .join('\n');
      } else {
        incomeCategoriesText = '• No income categories found';
      }

      const categoriesMessage = `
📂 *Your Personal Categories*

*💸 Expense Categories (${expenseCategories.length}):*
${expenseCategoriesText}

*💰 Income Categories (${incomeCategories.length}):*
${incomeCategoriesText}

*📝 Usage Examples:*
• \`/expense 500 ${expenseCategories[0]?.name.toLowerCase().replace(/[^a-z0-9]/g, '') || 'food'} Lunch at restaurant\`
• \`/income 50000 ${incomeCategories[0]?.name.toLowerCase().replace(/[^a-z0-9]/g, '') || 'salary'} Monthly salary\`

*🎯 Natural Language Examples:*
• "Spent 500 on ${expenseCategories[0]?.name || 'food'}"
• "Received 50000 from ${incomeCategories[0]?.name || 'salary'}"

*✨ Pro Tips:*
• Use category names exactly as shown above
• Categories are synced with your web app
• Add custom categories in the web app settings

💾 *Categories from your real FiNManageR account!*
      `;

      await this.bot.sendMessage(msg.chat.id, categoriesMessage, { parse_mode: 'Markdown' });
      await this.logInteraction(telegramUserId, '/categories', `Categories viewed: ${categories.length} total (${expenseCategories.length} expense, ${incomeCategories.length} income)`, true);

    } catch (error) {
      console.error('Categories command error:', error);
      await this.bot.sendMessage(msg.chat.id,
        "❌ Failed to retrieve categories. Please try again.\n\n" +
        "If the problem persists, contact support through the web app."
      );
    }
  }

  // Utility Methods
  async requireAuthentication(msg) {
    const telegramUserId = msg.from?.id.toString();
    if (!telegramUserId) return null;

    const { data: telegramUser } = await this.supabase
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', telegramUserId)
      .eq('is_active', true)
      .single();

    if (!telegramUser) {
      await this.bot.sendMessage(msg.chat.id,
        "🔗 *Let's Connect Your Account!*\n\n" +
        "To start tracking your finances, I need to connect to your FiNManageR account:\n\n" +
        "**Quick Setup:**\n" +
        "1️⃣ Open FiNManageR web app\n" +
        "2️⃣ Go to Settings → Telegram Integration\n" +
        "3️⃣ Click \"Generate Auth Code\"\n" +
        "4️⃣ Come back here and type: `/link <your_code>`\n\n" +
        "This keeps your financial data secure and synced across all devices! 🔒\n\n" +
        "Need assistance? We're here to help! 💬",
        { parse_mode: 'Markdown' }
      );
      return null;
    }

    return telegramUser;
  }

  async logInteraction(telegramUserId, command, description, success, error = null) {
    try {
      await this.supabase
        .from('telegram_interactions')
        .insert({
          telegram_user_id: telegramUserId,
          command: command,
          description: description,
          success: success,
          error_message: error,
          timestamp: new Date().toISOString()
        });
    } catch (logError) {
      console.error('Failed to log interaction:', logError);
    }
  }

  // Natural Language Processing
  async handleNaturalLanguageMessage(msg) {
    try {
      const telegramUserId = msg.from?.id.toString();
      if (!telegramUserId) return;

      // Check authentication
      const telegramUser = await this.requireAuthentication(msg);
      if (!telegramUser) return;

      // Parse natural language
      const nlpResult = this.parseNaturalLanguage(msg.text);

      if (nlpResult.success && nlpResult.transaction) {
        // Create enhanced transaction data for confirmation
        const transaction = nlpResult.transaction;
        const transactionData = {
          userId: telegramUser.user_id,
          amount: transaction.amount,
          type: transaction.type,
          category: transaction.category,
          description: transaction.description,
          date: new Date().toISOString(),
          confidence: nlpResult.confidence,
          originalMessage: msg.text,
          payment_method: this.detectPaymentMethod(msg.text),
          location: null,
          metadata: {
            telegram_user_id: telegramUserId,
            telegram_username: msg.from.username || null,
            source_type: 'telegram_bot_nlp'
          }
        };

        // Use enhanced confirmation service to show the advanced transaction form
        await this.enhancedConfirmationService.sendTransactionConfirmation(
          msg.chat.id,
          transactionData,
          'en', // Default language
          msg.message_id
        );

        console.log(`Enhanced NLP form shown: ₹${transaction.amount} ${transaction.category} for user ${telegramUser.user_id}`);
      } else {
        // Direct response - personality adaptation removed
        const suggestions = this.getNLPSuggestions(msg.text);
        let message = "🤔 I couldn't understand that transaction.\n\n" +
          "Try these formats:\n" +
          "• \"Spent 500 on lunch\"\n" +
          "• \"Paid 1200 for groceries\"\n" +
          "• \"Earned 50000 from salary\"\n\n" +
          "Or use commands:\n" +
          "• `/expense 500 food Lunch`\n" +
          "• `/income 50000 salary`\n\n" +
          (suggestions.length > 0 ? `💡 ${suggestions.join('\n💡 ')}` : '') +
          "\n\n💡 Try using specific commands like /expense or /income";

        await this.bot.sendMessage(msg.chat.id, message);
      }

      // Conversation context maintenance REMOVED - Enhanced Conversational AI eliminated

      await this.logInteraction(telegramUserId, 'nlp', `Processed: "${msg.text}"`, nlpResult.success);

    } catch (error) {
      console.error('Natural language processing error:', error);

      // Direct error response - personality adaptation removed
      await this.bot.sendMessage(msg.chat.id, "❌ I'm having trouble processing your message right now. Please try again or use specific commands.\n\n💡 Try: /expense 500 food Lunch or /income 50000 salary");
    }
  }

  parseNaturalLanguage(message) {
    const lowerMessage = message.toLowerCase();

    // Extract amount
    const amount = this.extractAmount(lowerMessage);
    if (!amount) {
      return { success: false, error: 'No amount found' };
    }

    // Determine transaction type
    const type = this.determineTransactionType(lowerMessage);
    if (!type) {
      return { success: false, error: 'Cannot determine transaction type' };
    }

    // Extract category
    const category = this.extractCategory(lowerMessage, type);
    if (!category) {
      return { success: false, error: 'Cannot determine category' };
    }

    // Extract description
    const description = this.extractDescription(message, amount, category);

    // Calculate confidence
    const confidence = this.calculateConfidence(lowerMessage, amount, type, category);

    return {
      success: true,
      confidence: confidence,
      transaction: {
        amount: amount,
        type: type,
        category: category,
        description: description
      }
    };
  }

  extractAmount(message) {
    // Look for various amount patterns
    const patterns = [
      /(?:₹|rs\.?|rupees?)\s*(\d+(?:,\d{3})*(?:\.\d{2})?)/i,
      /(\d+(?:,\d{3})*(?:\.\d{2})?)\s*(?:₹|rs\.?|rupees?)/i,
      /(\d+(?:,\d{3})*(?:\.\d{2})?)/
    ];

    for (const pattern of patterns) {
      const match = message.match(pattern);
      if (match) {
        const amount = parseFloat(match[1].replace(/,/g, ''));
        if (amount > 0 && amount < 10000000) { // Reasonable limits
          return amount;
        }
      }
    }

    return null;
  }

  determineTransactionType(message) {
    const expenseKeywords = ['spent', 'paid', 'bought', 'purchase', 'expense', 'cost', 'bill'];
    const incomeKeywords = ['earned', 'received', 'income', 'salary', 'got', 'profit'];

    const hasExpenseKeyword = expenseKeywords.some(keyword => message.includes(keyword));
    const hasIncomeKeyword = incomeKeywords.some(keyword => message.includes(keyword));

    if (hasExpenseKeyword && !hasIncomeKeyword) return 'expense';
    if (hasIncomeKeyword && !hasExpenseKeyword) return 'income';

    // Default to expense if ambiguous
    return 'expense';
  }

  extractCategory(message, type) {
    const expenseCategories = {
      'food': ['food', 'lunch', 'dinner', 'breakfast', 'meal', 'restaurant', 'cafe', 'snack'],
      'transport': ['transport', 'uber', 'taxi', 'bus', 'train', 'fuel', 'petrol', 'diesel'],
      'entertainment': ['movie', 'cinema', 'game', 'entertainment', 'fun', 'party'],
      'shopping': ['shopping', 'clothes', 'shirt', 'shoes', 'dress', 'mall'],
      'utilities': ['electricity', 'water', 'gas', 'internet', 'phone', 'bill'],
      'healthcare': ['doctor', 'medicine', 'hospital', 'health', 'medical'],
      'education': ['book', 'course', 'education', 'school', 'college'],
      'travel': ['travel', 'trip', 'hotel', 'flight', 'vacation'],
      'other': ['other', 'misc', 'miscellaneous']
    };

    const incomeCategories = {
      'salary': ['salary', 'wage', 'pay', 'paycheck'],
      'freelance': ['freelance', 'project', 'client', 'contract'],
      'investment': ['investment', 'dividend', 'interest', 'profit'],
      'business': ['business', 'sale', 'revenue'],
      'other': ['other', 'misc', 'miscellaneous']
    };

    const categories = type === 'expense' ? expenseCategories : incomeCategories;

    for (const [category, keywords] of Object.entries(categories)) {
      if (keywords.some(keyword => message.includes(keyword))) {
        return category;
      }
    }

    return 'other';
  }

  extractDescription(message, amount, category) {
    // Remove amount and common words to get description
    let description = message
      .replace(/₹?\s*\d+(?:,\d{3})*(?:\.\d{2})?\s*₹?/g, '')
      .replace(/\b(spent|paid|bought|earned|received|for|on|from)\b/gi, '')
      .replace(/\s+/g, ' ')
      .trim();

    return description || `${category} transaction`;
  }

  calculateConfidence(message, amount, type, category) {
    let confidence = 0.5; // Base confidence

    // Higher confidence for clear keywords
    if (message.includes('spent') || message.includes('paid') || message.includes('earned')) {
      confidence += 0.2;
    }

    // Higher confidence for specific categories
    if (category !== 'other') {
      confidence += 0.15;
    }

    // Higher confidence for reasonable amounts
    if (amount >= 10 && amount <= 100000) {
      confidence += 0.15;
    }

    return Math.min(confidence, 0.95);
  }

  detectPaymentMethod(message) {
    const paymentKeywords = {
      'Cash': ['cash', 'money', 'notes'],
      'Credit Card': ['credit', 'card', 'visa', 'mastercard'],
      'Debit Card': ['debit', 'atm'],
      'UPI/Digital Wallet': ['upi', 'paytm', 'gpay', 'phonepe', 'digital'],
      'Bank Transfer': ['transfer', 'neft', 'rtgs', 'imps'],
      'Online Payment': ['online', 'net banking', 'internet']
    };

    const lowerMessage = message.toLowerCase();

    for (const [method, keywords] of Object.entries(paymentKeywords)) {
      if (keywords.some(keyword => lowerMessage.includes(keyword))) {
        return method;
      }
    }

    return 'Cash'; // Default
  }

  getNLPSuggestions(message) {
    const suggestions = [];

    if (!this.extractAmount(message)) {
      suggestions.push("💡 Include the amount: 'Spent ₹500 on lunch'");
    }

    return suggestions;
  }

  // Enhanced photo handler with attachment support
  async handlePhotoMessage(msg) {
    const telegramUserId = msg.from?.id.toString();
    if (!telegramUserId || !msg.photo) return;

    try {
      // Check authentication
      const telegramUser = await this.requireAuthentication(msg);
      if (!telegramUser) return;

      // Check if GCS is configured
      if (!this.attachmentService.isConfigured()) {
        // Fallback to manual entry guidance
        const quickResponse = `
📸 *Photo Received!*

Thanks for sharing your receipt/order image!

For fastest expense tracking, please use:

**💬 Natural Language:**
• "Spent 500 on food at McDonald's"
• "Paid 1200 for groceries at BigBasket"
• "Amazon order 899 for electronics"

**⚡ Quick Commands:**
• \`/expense 500 food McDonald's\`
• \`/expense 1200 groceries BigBasket\`
• \`/expense 899 electronics Amazon\`

*This approach is much faster than image processing!* ⚡
        `;

        await this.bot.sendMessage(msg.chat.id, quickResponse, { parse_mode: 'Markdown' });
        return;
      }

      // Attachment-enabled response
      const attachmentResponse = `
📸 *Photo Received!*

I can help you in two ways:

**🔗 Option 1: Save as Attachment**
Use the buttons below to create a transaction with this photo attached.

**💬 Option 2: Manual Entry**
• "Spent 500 on food at McDonald's"
• \`/expense 500 food McDonald's\`

Choose your preferred method:
      `;

      const keyboard = {
        inline_keyboard: [
          [
            { text: '💰 Expense + Attachment', callback_data: `attach_expense_${Date.now()}` },
            { text: '💵 Income + Attachment', callback_data: `attach_income_${Date.now()}` }
          ],
          [
            { text: '🏦 Loan + Attachment', callback_data: `attach_loan_${Date.now()}` },
            { text: '📈 Investment + Attachment', callback_data: `attach_investment_${Date.now()}` }
          ],
          [
            { text: '❌ Cancel', callback_data: `cancel_attachment_${Date.now()}` }
          ]
        ]
      };

      // Store photo for attachment processing
      const photoFileId = msg.photo[msg.photo.length - 1].file_id;
      this.pendingAttachments.set(telegramUserId, {
        fileId: photoFileId,
        fileType: 'photo',
        timestamp: Date.now(),
        telegramUser
      });

      await this.bot.sendMessage(msg.chat.id, attachmentResponse, {
        parse_mode: 'Markdown',
        reply_markup: keyboard
      });

      // Log interaction
      await this.logInteraction(
        telegramUserId,
        'photo_received',
        'Photo received - guided to manual entry',
        true,
        null,
        0,
        { file_id: msg.photo[msg.photo.length - 1].file_id }
      );

    } catch (error) {
      console.error('Photo handling error:', error);
      await this.bot.sendMessage(msg.chat.id,
        "📸 Photo received! Please use manual entry for fastest results:\n\n" +
        "• Natural language: \"Spent 500 on food\"\n" +
        "• Quick command: `/expense 500 food description`"
      );
    }
  }

  // Document handler for PDF and text attachments
  async handleDocumentMessage(msg) {
    const telegramUserId = msg.from?.id.toString();
    if (!telegramUserId || !msg.document) return;

    try {
      // Check authentication
      const telegramUser = await this.requireAuthentication(msg);
      if (!telegramUser) return;

      // Check if GCS is configured
      if (!this.attachmentService.isConfigured()) {
        await this.bot.sendMessage(msg.chat.id,
          "📎 Document received! However, attachment storage is not configured.\n\n" +
          "Please use manual entry for your transaction."
        );
        return;
      }

      // Check file type
      const supportedTypes = this.attachmentService.getSupportedFileTypes();
      const fileExtension = msg.document.file_name ?
        msg.document.file_name.split('.').pop().toLowerCase() : '';

      if (!['pdf', 'txt'].includes(fileExtension)) {
        await this.bot.sendMessage(msg.chat.id,
          `📎 Document received, but only PDF and TXT files are supported.\n\n` +
          `Supported types: ${supportedTypes.description}`
        );
        return;
      }

      // Document attachment response
      const attachmentResponse = `
📎 *Document Received!*

**File:** ${msg.document.file_name}
**Size:** ${(msg.document.file_size / 1024).toFixed(1)} KB

Choose transaction type to attach this document:
      `;

      const keyboard = {
        inline_keyboard: [
          [
            { text: '💰 Expense + Document', callback_data: `attach_expense_${Date.now()}` },
            { text: '💵 Income + Document', callback_data: `attach_income_${Date.now()}` }
          ],
          [
            { text: '🏦 Loan + Document', callback_data: `attach_loan_${Date.now()}` },
            { text: '📈 Investment + Document', callback_data: `attach_investment_${Date.now()}` }
          ],
          [
            { text: '❌ Cancel', callback_data: `cancel_attachment_${Date.now()}` }
          ]
        ]
      };

      // Store document for attachment processing
      this.pendingAttachments.set(telegramUserId, {
        fileId: msg.document.file_id,
        fileType: 'document',
        fileName: msg.document.file_name,
        fileSize: msg.document.file_size,
        timestamp: Date.now(),
        telegramUser
      });

      await this.bot.sendMessage(msg.chat.id, attachmentResponse, {
        parse_mode: 'Markdown',
        reply_markup: keyboard
      });

    } catch (error) {
      console.error('Document handling error:', error);
      await this.bot.sendMessage(msg.chat.id,
        "📎 Document received! Please use manual entry for your transaction."
      );
    }
  }

  // Attachment callback handlers
  async handleAttachmentCallback(callbackQuery) {
    const { data, from, message } = callbackQuery;
    const telegramUserId = from.id.toString();

    try {
      // Get pending attachment
      const pendingAttachment = this.pendingAttachments?.get(telegramUserId);
      if (!pendingAttachment) {
        await this.bot.answerCallbackQuery(callbackQuery.id, {
          text: "❌ Attachment data expired. Please send the file again.",
          show_alert: true
        });
        return;
      }

      if (data.startsWith('cancel_attachment_')) {
        // Cancel attachment processing
        this.pendingAttachments.delete(telegramUserId);

        await this.bot.editMessageText(
          "❌ Attachment cancelled.\n\nYou can send another file or use manual entry commands.",
          {
            chat_id: message.chat.id,
            message_id: message.message_id
          }
        );

        await this.bot.answerCallbackQuery(callbackQuery.id, {
          text: "Attachment cancelled"
        });
        return;
      }

      // Extract transaction type
      let transactionType = '';
      if (data.includes('attach_expense_')) transactionType = 'expense';
      else if (data.includes('attach_income_')) transactionType = 'income';
      else if (data.includes('attach_loan_')) transactionType = 'loan';
      else if (data.includes('attach_investment_')) transactionType = 'investment';

      if (!transactionType) {
        await this.bot.answerCallbackQuery(callbackQuery.id, {
          text: "❌ Invalid transaction type",
          show_alert: true
        });
        return;
      }

      // Start transaction creation with attachment
      await this.startTransactionWithAttachment(callbackQuery, transactionType, pendingAttachment);

    } catch (error) {
      console.error('Attachment callback error:', error);
      await this.bot.answerCallbackQuery(callbackQuery.id, {
        text: "❌ Error processing attachment. Please try again.",
        show_alert: true
      });
    }
  }

  // Start transaction creation with attachment
  async startTransactionWithAttachment(callbackQuery, transactionType, pendingAttachment) {
    const { message, from } = callbackQuery;
    const telegramUserId = from.id.toString();

    try {
      // Update message to show transaction form
      const formMessage = `
📝 *Create ${transactionType.charAt(0).toUpperCase() + transactionType.slice(1)} with Attachment*

**Attachment:** ${pendingAttachment.fileName || 'Photo'} ${pendingAttachment.fileType === 'photo' ? '📷' : '📎'}

Please provide the transaction details:

**Format:** \`amount category description\`
**Example:** \`500 food McDonald's lunch\`

Or use natural language:
**Example:** "Spent 500 on food at McDonald's"

Type your transaction details below:
      `;

      await this.bot.editMessageText(formMessage, {
        chat_id: message.chat.id,
        message_id: message.message_id,
        parse_mode: 'Markdown'
      });

      // Store transaction context
      this.pendingAttachments.set(telegramUserId, {
        ...pendingAttachment,
        transactionType,
        awaitingDetails: true
      });

      await this.bot.answerCallbackQuery(callbackQuery.id, {
        text: `Creating ${transactionType} with attachment`
      });

    } catch (error) {
      console.error('Error starting transaction with attachment:', error);
      await this.bot.answerCallbackQuery(callbackQuery.id, {
        text: "❌ Error starting transaction. Please try again.",
        show_alert: true
      });
    }
  }

  // Enhanced NLP handler to support attachment context
  async handleNaturalLanguageMessage(msg) {
    const telegramUserId = msg.from?.id.toString();
    if (!telegramUserId) return;

    try {
      // Check if user is in attachment flow
      const pendingAttachment = this.pendingAttachments?.get(telegramUserId);
      if (pendingAttachment && pendingAttachment.awaitingDetails) {
        await this.processTransactionWithAttachment(msg, pendingAttachment);
        return;
      }

      // Regular NLP processing (existing code)
      await this.processNaturalLanguageTransaction(msg);

    } catch (error) {
      console.error('NLP processing error:', error);
      await this.bot.sendMessage(msg.chat.id,
        "❌ Sorry, I had trouble understanding your message.\n\n" +
        "Try using commands like `/expense 500 food` or `/income 50000 salary`"
      );
    }
  }

  // Process transaction with attachment
  async processTransactionWithAttachment(msg, pendingAttachment) {
    const telegramUserId = msg.from?.id.toString();

    try {
      // Parse transaction details from message
      const transactionData = await this.parseTransactionMessage(msg.text, pendingAttachment.transactionType);

      if (!transactionData.success) {
        await this.bot.sendMessage(msg.chat.id,
          `❌ ${transactionData.error}\n\n` +
          "Please try again with format: `amount category description`\n" +
          "Example: `500 food McDonald's lunch`"
        );
        return;
      }

      // Show processing message
      await this.bot.sendMessage(msg.chat.id,
        "🔄 Creating transaction and uploading attachment...\n" +
        "This may take a few seconds."
      );

      // Upload attachment to GCS
      const uploadResult = await this.attachmentService.uploadTelegramFile(
        pendingAttachment.fileId,
        pendingAttachment.transactionType,
        telegramUserId,
        null, // transactionId - will be set after transaction creation
        this.bot.token
      );

      if (!uploadResult.success) {
        await this.bot.sendMessage(msg.chat.id,
          `❌ Failed to upload attachment: ${uploadResult.error}\n\n` +
          "Transaction will be created without attachment."
        );
      }

      // Create transaction in database
      const transaction = {
        user_id: pendingAttachment.telegramUser.user_id,
        amount: transactionData.amount,
        type: pendingAttachment.transactionType,
        category: transactionData.category,
        source: transactionData.description,
        description: transactionData.description,
        attachments: uploadResult.success ? [uploadResult.url] : [],
        metadata: {
          telegram_created: true,
          has_attachment: uploadResult.success,
          attachment_info: uploadResult.success ? {
            file_id: uploadResult.file.id,
            file_name: uploadResult.file.name,
            file_size: uploadResult.file.size,
            mime_type: uploadResult.file.mimeType
          } : null
        }
      };

      const { data: savedTransaction, error } = await this.supabase
        .from('transactions')
        .insert([transaction])
        .select()
        .single();

      if (error) throw error;

      // Clear pending attachment
      this.pendingAttachments.delete(telegramUserId);

      // Success message
      const successMessage = `
✅ *${pendingAttachment.transactionType.charAt(0).toUpperCase() + pendingAttachment.transactionType.slice(1)} Created Successfully!*

💰 **Amount:** ₹${transactionData.amount}
📂 **Category:** ${transactionData.category}
📝 **Description:** ${transactionData.description}
${uploadResult.success ? `📎 **Attachment:** ${uploadResult.file.name}` : '⚠️ **Attachment:** Upload failed'}

Transaction ID: \`${savedTransaction.id}\`

Use \`/recent\` to view recent transactions.
      `;

      await this.bot.sendMessage(msg.chat.id, successMessage, { parse_mode: 'Markdown' });

    } catch (error) {
      console.error('Transaction with attachment error:', error);
      await this.bot.sendMessage(msg.chat.id,
        "❌ Failed to create transaction with attachment.\n\n" +
        "Please try again or use manual entry without attachment."
      );
    }
  }

  // Parse transaction message for attachment flow
  async parseTransactionMessage(text, transactionType) {
    try {
      // Try to extract amount, category, and description
      const patterns = [
        // Pattern: "500 food McDonald's lunch"
        /^(\d+(?:\.\d{2})?)\s+(\w+)\s+(.+)$/,
        // Pattern: "500 food"
        /^(\d+(?:\.\d{2})?)\s+(\w+)$/,
        // Pattern: "spent 500 on food at McDonald's"
        /(?:spent|paid|received|earned)\s+(\d+(?:\.\d{2})?)\s+(?:on|for|from)\s+(\w+)(?:\s+at\s+(.+))?/i,
        // Pattern: "500 for food"
        /^(\d+(?:\.\d{2})?)\s+(?:for|on)\s+(\w+)(?:\s+(.+))?$/i
      ];

      for (const pattern of patterns) {
        const match = text.match(pattern);
        if (match) {
          const amount = parseFloat(match[1]);
          const category = match[2].toLowerCase();
          const description = match[3] || `${transactionType} via Telegram`;

          if (amount > 0 && amount < 10000000) {
            return {
              success: true,
              amount,
              category,
              description: description.trim()
            };
          }
        }
      }

      return {
        success: false,
        error: "Could not parse transaction details. Please use format: amount category description"
      };

    } catch (error) {
      return {
        success: false,
        error: "Error parsing transaction message"
      };
    }
  }



  async handleAdvancedVoiceMessage(msg) {
    try {
      const telegramUserId = msg.from?.id.toString();
      if (!telegramUserId) return;

      // Check authentication
      const telegramUser = await this.requireAuthentication(msg);
      if (!telegramUser) return;

      await this.bot.sendMessage(msg.chat.id, "🎤 Processing your voice command...");

      // Get audio file URL from Telegram
      const audioUrl = await this.getAudioFileUrl(msg.voice.file_id);

      // Get user's language preference (default to English)
      const userLanguage = 'en'; // Could be retrieved from user preferences

      // Get conversation context
      const navigationState = this.advancedVoiceIntelligence.getVoiceNavigationState(telegramUserId);

      const context = {
        currentMenu: navigationState,
        generateVoiceResponse: false, // Set to true for audio responses
        hasRecentTransactions: true, // Could check actual data
        isNewUser: false,
        startTime: Date.now()
      };

      // Process with advanced voice intelligence
      const result = await this.advancedVoiceIntelligence.processAdvancedVoiceMessage(
        audioUrl,
        telegramUserId,
        userLanguage,
        context
      );

      if (result.success) {
        // Direct response - personality adaptation removed
        let message = result.result.response;
        if (result.result.nextActions?.length > 0) {
          message += `\n\n💡 ${result.result.nextActions.join(', ')}`;
        } else {
          message += "\n\n💡 Try voice commands like 'show balance' or 'add expense'";
        }

        await this.bot.sendMessage(msg.chat.id, message);

        // Handle specific actions
        if (result.result.transaction) {
          await this.processVoiceTransaction(msg, result.result.transaction, telegramUser);
        }

        if (result.result.audioUrl) {
          await this.bot.sendVoice(msg.chat.id, result.result.audioUrl);
        }

        // Conversation context maintenance REMOVED - Enhanced Conversational AI eliminated

      } else {
        // Direct error response - personality adaptation removed
        let errorMessage = result.error || "I couldn't understand your voice command.";
        errorMessage += `\n\n💡 ${result.suggestion || "Try speaking more clearly or use text commands"}`;

        await this.bot.sendMessage(msg.chat.id, errorMessage);
      }

      await this.logInteraction(telegramUserId, 'voice', `Processed voice: "${result.transcription || 'failed'}"`, result.success);

    } catch (error) {
      console.error('Advanced voice processing error:', error);

      // Direct error response - personality adaptation removed
      await this.bot.sendMessage(msg.chat.id, "❌ Error processing voice message. Please try again or use text commands.\n\n💡 Try: /expense 500 food Lunch or 'spent 500 on lunch'");
    }
  }

  /**
   * Get audio file URL from Telegram
   */
  async getAudioFileUrl(fileId) {
    try {
      const file = await this.bot.getFile(fileId);
      return `https://api.telegram.org/file/bot${this.bot.token}/${file.file_path}`;
    } catch (error) {
      throw new Error(`Failed to get audio file URL: ${error.message}`);
    }
  }

  /**
   * Process voice-detected transaction
   */
  async processVoiceTransaction(msg, transaction, telegramUser) {
    try {
      // Add transaction to database
      const { data, error } = await this.supabase
        .from('transactions')
        .insert({
          user_id: telegramUser.user_id,
          type: transaction.type,
          amount: transaction.amount,
          description: transaction.description,
          category: transaction.category,
          date: transaction.date,
          source_type: transaction.source_type
        })
        .select()
        .single();

      if (error) throw error;

      // Send confirmation with enhanced formatting
      const confirmationMessage = `✅ Voice transaction recorded!\n\n` +
        `💰 **${transaction.type === 'expense' ? 'Expense' : 'Income'}:** ₹${transaction.amount.toLocaleString()}\n` +
        `📝 **Description:** ${transaction.description}\n` +
        `🏷️ **Category:** ${transaction.category}\n` +
        `📅 **Date:** ${new Date(transaction.date).toLocaleDateString()}\n\n` +
        `🎤 **Source:** Voice Command`;

      await this.bot.sendMessage(msg.chat.id, confirmationMessage, { parse_mode: 'Markdown' });

    } catch (error) {
      console.error('Error processing voice transaction:', error);
      await this.bot.sendMessage(msg.chat.id,
        "❌ Error saving transaction. Please try again or use manual entry."
      );
    }
  }

  async handleInsightsCommand(msg) {
    try {
      const telegramUserId = msg.from?.id.toString();
      if (!telegramUserId) return;

      // Check authentication
      const telegramUser = await this.requireAuthentication(msg);
      if (!telegramUser) return;

      await this.bot.sendMessage(msg.chat.id, "🧠 Analyzing your spending patterns...");

      // Get user's transaction data for analysis
      const now = new Date();
      const thirtyDaysAgo = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000));
      const sixtyDaysAgo = new Date(now.getTime() - (60 * 24 * 60 * 60 * 1000));

      // Get current month and previous month data
      const { data: currentMonthTransactions } = await this.supabase
        .from('transactions')
        .select('*')
        .eq('user_id', telegramUser.user_id)
        .gte('date', thirtyDaysAgo.toISOString())
        .order('date', { ascending: false });

      const { data: previousMonthTransactions } = await this.supabase
        .from('transactions')
        .select('*')
        .eq('user_id', telegramUser.user_id)
        .gte('date', sixtyDaysAgo.toISOString())
        .lt('date', thirtyDaysAgo.toISOString());

      if (!currentMonthTransactions || currentMonthTransactions.length === 0) {
        await this.bot.sendMessage(msg.chat.id,
          "🔮 *Your Personal Financial Insights Await!*\n\n" +
          "I'm ready to analyze your spending patterns and provide personalized insights, but I need some transaction data first.\n\n" +
          "**Start building your financial story:**\n" +
          "💰 `/expense 500 food Lunch at downtown cafe`\n" +
          "💵 `/income 75000 salary Monthly paycheck`\n" +
          "💬 Or simply say: \"Bought groceries for 1200 today\"\n\n" +
          "Once you have a few transactions, I'll show you amazing insights about your spending habits! 📈✨",
          { parse_mode: 'Markdown' }
        );
        return;
      }

      // Generate AI insights
      const insights = this.generateAIInsights(currentMonthTransactions, previousMonthTransactions);

      let baseInsightsMessage = `
🧠 *AI-Powered Financial Insights*

${insights.summary}

📊 **Spending Analysis (Last 30 Days)**
${insights.spendingAnalysis}

📈 **Trends & Patterns**
${insights.trends}

💡 **Smart Recommendations**
${insights.recommendations}

🎯 **Action Items**
${insights.actionItems}

📱 **Quick Actions:**
• \`/balance\` - Check current balance
• \`/recent\` - View recent transactions
• \`/expense\` - Log new expense

💾 *Analysis based on your real FiNManageR data*
      `;

      // Direct insights message - personality adaptation removed
      let insightsMessage = baseInsightsMessage + "\n\n💡 Quick actions: /balance, /recent, /expense";

      await this.bot.sendMessage(msg.chat.id, insightsMessage, { parse_mode: 'Markdown' });

      // Conversation context maintenance REMOVED - Enhanced Conversational AI eliminated

      await this.logInteraction(telegramUserId, '/insights', `Generated AI insights for ${currentMonthTransactions.length} transactions`, true);

    } catch (error) {
      console.error('AI insights error:', error);
      await this.bot.sendMessage(msg.chat.id,
        "❌ Failed to generate insights. Please try again.\n\n" +
        "If the problem persists, contact support through the web app."
      );
    }
  }

  generateAIInsights(currentTransactions, previousTransactions) {
    // Calculate basic metrics
    const currentExpenses = currentTransactions.filter(t => t.type === 'expense');
    const currentIncome = currentTransactions.filter(t => t.type === 'income');
    const previousExpenses = previousTransactions?.filter(t => t.type === 'expense') || [];

    const totalExpenses = currentExpenses.reduce((sum, t) => sum + t.amount, 0);
    const totalIncome = currentIncome.reduce((sum, t) => sum + t.amount, 0);
    const previousTotalExpenses = previousExpenses.reduce((sum, t) => sum + t.amount, 0);

    const netBalance = totalIncome - totalExpenses;
    const expenseChange = previousTotalExpenses > 0
      ? ((totalExpenses - previousTotalExpenses) / previousTotalExpenses * 100).toFixed(1)
      : 0;

    // Category analysis
    const categorySpending = currentExpenses.reduce((acc, t) => {
      acc[t.category] = (acc[t.category] || 0) + t.amount;
      return acc;
    }, {});

    const topCategories = Object.entries(categorySpending)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3);

    // Generate insights
    const summary = this.generateSummaryInsight(totalExpenses, totalIncome, netBalance, currentTransactions.length);
    const spendingAnalysis = this.generateSpendingAnalysis(totalExpenses, topCategories, currentExpenses.length);
    const trends = this.generateTrendsInsight(expenseChange, currentTransactions, previousTransactions);
    const recommendations = this.generateRecommendations(categorySpending, netBalance, totalExpenses);
    const actionItems = this.generateActionItems(netBalance, topCategories, currentTransactions);

    return {
      summary,
      spendingAnalysis,
      trends,
      recommendations,
      actionItems
    };
  }

  generateSummaryInsight(totalExpenses, totalIncome, netBalance, transactionCount) {
    const balanceEmoji = netBalance >= 0 ? '✅' : '⚠️';
    const balanceText = netBalance >= 0 ? 'positive' : 'negative';

    return `${balanceEmoji} You have a **${balanceText} balance** of ₹${Math.abs(netBalance).toLocaleString()} this month.\n` +
           `📊 **${transactionCount} transactions** analyzed with ₹${totalExpenses.toLocaleString()} in expenses and ₹${totalIncome.toLocaleString()} in income.`;
  }

  generateSpendingAnalysis(totalExpenses, topCategories, expenseCount) {
    if (topCategories.length === 0) {
      return "• No expense data available for analysis";
    }

    const avgPerTransaction = totalExpenses / expenseCount;
    let analysis = `• **Total Expenses:** ₹${totalExpenses.toLocaleString()}\n`;
    analysis += `• **Average per transaction:** ₹${avgPerTransaction.toFixed(0)}\n`;
    analysis += `• **Top spending categories:**\n`;

    topCategories.forEach(([category, amount], index) => {
      const percentage = ((amount / totalExpenses) * 100).toFixed(1);
      const emoji = ['🥇', '🥈', '🥉'][index];
      analysis += `  ${emoji} ${category.charAt(0).toUpperCase() + category.slice(1)}: ₹${amount.toLocaleString()} (${percentage}%)\n`;
    });

    return analysis;
  }

  generateTrendsInsight(expenseChange, currentTransactions, previousTransactions) {
    let trends = "";

    if (previousTransactions && previousTransactions.length > 0) {
      const changeEmoji = expenseChange > 0 ? '📈' : expenseChange < 0 ? '📉' : '➡️';
      const changeText = expenseChange > 0 ? 'increased' : expenseChange < 0 ? 'decreased' : 'remained stable';
      trends += `${changeEmoji} Spending has **${changeText}** by ${Math.abs(expenseChange)}% compared to last month\n`;
    }

    // Analyze spending frequency
    const daysWithTransactions = new Set(currentTransactions.map(t =>
      new Date(t.date).toDateString()
    )).size;

    const avgTransactionsPerDay = (currentTransactions.length / 30).toFixed(1);
    trends += `📅 Active spending on **${daysWithTransactions} days** this month\n`;
    trends += `⚡ Average **${avgTransactionsPerDay} transactions per day**`;

    return trends;
  }

  generateRecommendations(categorySpending, netBalance, totalExpenses) {
    let recommendations = "";

    // Budget recommendations
    if (netBalance < 0) {
      recommendations += "🚨 **Budget Alert:** You're spending more than earning\n";
      recommendations += "💡 Consider reducing expenses or increasing income\n";
    } else if (netBalance < totalExpenses * 0.2) {
      recommendations += "⚠️ **Low Savings:** Consider saving more for emergencies\n";
    } else {
      recommendations += "💰 **Good Savings:** You're maintaining healthy finances\n";
    }

    // Category-specific recommendations
    const topCategory = Object.entries(categorySpending)
      .sort(([,a], [,b]) => b - a)[0];

    if (topCategory) {
      const [category, amount] = topCategory;
      const percentage = (amount / totalExpenses * 100).toFixed(1);

      if (percentage > 40) {
        recommendations += `🎯 **${category.charAt(0).toUpperCase() + category.slice(1)}** takes ${percentage}% of expenses - consider optimizing`;
      }
    }

    return recommendations || "✅ Your spending patterns look healthy!";
  }

  generateActionItems(netBalance, topCategories, transactions) {
    let actionItems = "";

    if (netBalance < 0) {
      actionItems += "1️⃣ **Review and reduce** top expense categories\n";
      actionItems += "2️⃣ **Set spending limits** for high-expense categories\n";
      actionItems += "3️⃣ **Track daily expenses** more closely\n";
    } else {
      actionItems += "1️⃣ **Continue current habits** - you're doing well!\n";
      actionItems += "2️⃣ **Consider investing** your surplus\n";
      actionItems += "3️⃣ **Set savings goals** for future planning\n";
    }

    // Recent transaction insights
    const recentHighExpenses = transactions
      .filter(t => t.type === 'expense' && t.amount > 1000)
      .length;

    if (recentHighExpenses > 0) {
      actionItems += `4️⃣ **Review ${recentHighExpenses} high-value transactions** (>₹1000)`;
    }

    return actionItems;
  }

  async handlePredictiveAnalysisCommand(msg) {
    try {
      const telegramUserId = msg.from?.id.toString();
      if (!telegramUserId) return;

      // Check authentication
      const telegramUser = await this.requireAuthentication(msg);
      if (!telegramUser) return;

      await this.bot.sendMessage(msg.chat.id, "🔮 Running predictive analysis...");

      // Trigger predictive analysis for this user
      await this.predictiveAlertSystem.analyzeUserRisks(telegramUser);

      // Direct response - personality adaptation removed
      const message = "✅ Predictive analysis complete! I've analyzed your financial patterns and will send proactive alerts if any risks are detected.\n\n💡 /insights for detailed analysis, /balance to check current status";

      await this.bot.sendMessage(msg.chat.id, message);

      // Conversation context maintenance REMOVED - Enhanced Conversational AI eliminated

      await this.logInteraction(telegramUserId, '/predict', 'Predictive analysis triggered', true);

    } catch (error) {
      console.error('Predictive analysis error:', error);

      // Direct error response - personality adaptation removed
      await this.bot.sendMessage(msg.chat.id, "❌ Error running predictive analysis. Please try again later.\n\n💡 Try /insights for current financial analysis");
    }
  }

  async handleVoiceCommandsHelp(msg) {
    try {
      const telegramUserId = msg.from?.id.toString();
      if (!telegramUserId) return;

      // Check authentication
      const telegramUser = await this.requireAuthentication(msg);
      if (!telegramUser) return;

      // Get voice commands help from advanced voice intelligence
      const helpResult = await this.advancedVoiceIntelligence.processHelpCommand(
        { type: 'help', parameters: {} },
        telegramUserId,
        'en',
        {}
      );

      // Direct help response - personality adaptation removed
      let helpMessage = helpResult.response + "\n\n💡 Try recording a voice message with commands like 'show balance' or 'spent 500 on lunch'";

      await this.bot.sendMessage(msg.chat.id, helpMessage);

      // Conversation context maintenance REMOVED - Enhanced Conversational AI eliminated

      await this.logInteraction(telegramUserId, '/voice', 'Voice commands help requested', true);

    } catch (error) {
      console.error('Voice commands help error:', error);

      // Direct error response - personality adaptation removed
      await this.bot.sendMessage(msg.chat.id, "❌ Error showing voice commands help. Please try again.\n\n💡 Try /help for general assistance");
    }
  }

  async handleSmartSchedulingCommand(msg) {
    try {
      const telegramUserId = msg.from?.id.toString();
      if (!telegramUserId) return;

      // Check authentication
      const telegramUser = await this.requireAuthentication(msg);
      if (!telegramUser) return;

      await this.bot.sendMessage(msg.chat.id, "📅 Creating intelligent schedule...");

      // Create intelligent schedule for expense logging reminders
      const scheduleResult = await this.smartSchedulingAI.createIntelligentSchedule(
        telegramUser.user_id,
        'expense_logging',
        {
          userPreferences: {
            reminderFrequency: 'medium',
            preferredTimes: [9, 18],
            communicationStyle: 'friendly'
          }
        }
      );

      if (scheduleResult.success) {
        // Enhanced AI: Generate personality-adapted response
        let baseMessage = `✅ **Smart Schedule Created!**\n\n` +
          `📊 **Schedule Type:** Expense Logging Reminders\n` +
          `⏰ **Optimal Time:** ${scheduleResult.schedule.primaryTime || '9:00 AM'}\n` +
          `🔄 **Frequency:** ${scheduleResult.schedule.frequency || 'Daily'}\n` +
          `🎯 **Expected Effectiveness:** ${Math.round((scheduleResult.effectiveness || 0.8) * 100)}%\n\n` +
          `🧠 **AI Reasoning:**\n` +
          `• ${scheduleResult.reasoning?.optimalTiming || 'Based on typical user activity patterns'}\n` +
          `• ${scheduleResult.reasoning?.adaptiveFrequency || 'Optimized for user engagement'}\n\n` +
          `📅 **Next Reminder:** ${scheduleResult.nextReminder ? new Date(scheduleResult.nextReminder).toLocaleString() : 'Soon'}`;

        let message = baseMessage + "\n\n💡 Use /schedule to create more schedules, /voice for voice commands";

        await this.bot.sendMessage(msg.chat.id, message, { parse_mode: 'Markdown' });

        // Show available reminder types
        const reminderTypesMessage = `🔔 **Available Reminder Types:**\n\n` +
          `💰 Expense Logging - Daily expense tracking\n` +
          `📊 Budget Check - Weekly budget monitoring\n` +
          `🎯 Savings Goal - Progress tracking\n` +
          `📋 Bill Payment - Monthly bill reminders\n` +
          `🔍 Financial Review - Monthly financial analysis\n` +
          `📈 Investment Check - Portfolio monitoring\n\n` +
          `💡 The AI will learn your patterns and optimize reminder timing automatically!`;

        await this.bot.sendMessage(msg.chat.id, reminderTypesMessage, { parse_mode: 'Markdown' });

      } else {
        let errorMessage = scheduleResult.error || "❌ Error creating intelligent schedule. Using fallback schedule.";
        errorMessage += "\n\n💡 Try again later or use manual reminders";

        await this.bot.sendMessage(msg.chat.id, errorMessage);
      }

      // Conversation context maintenance REMOVED - Enhanced Conversational AI eliminated

      await this.logInteraction(telegramUserId, '/schedule', 'Smart scheduling requested', scheduleResult.success);

    } catch (error) {
      console.error('Smart scheduling error:', error);

      // Direct error response - personality adaptation removed
      await this.bot.sendMessage(msg.chat.id, "❌ Error creating smart schedule. Please try again later.\n\n💡 Try /help for other available commands");
    }
  }

  async handleConfirmTransaction(msg, confirmed) {
    // This is handled by the enhanced confirmation service now
    await this.bot.sendMessage(msg.chat.id,
      "Please use the inline buttons on transaction forms for confirmation."
    );
  }
}

// Initialize and start the bot
const token = process.env.TELEGRAM_BOT_TOKEN;
if (!token) {
  console.error('❌ TELEGRAM_BOT_TOKEN is required');
  process.exit(1);
}

const bot = new ProductionTelegramBot(token);
console.log('🚀 FiNManageR Bot is now online and ready to help users manage their finances!');

module.exports = ProductionTelegramBot;
